"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */\\n@layer properties;\\n@layer theme, base, components, utilities;\\n@layer theme {\\n  :root, :host {\\n    --color-amber-400: oklch(82.8% 0.189 84.429);\\n    --color-green-500: oklch(72.3% 0.219 149.579);\\n    --color-green-700: oklch(52.7% 0.154 150.069);\\n    --color-indigo-500: oklch(58.5% 0.233 277.117);\\n    --color-slate-200: oklch(92.9% 0.013 255.508);\\n    --color-slate-300: oklch(86.9% 0.022 252.894);\\n    --color-slate-400: oklch(70.4% 0.04 256.788);\\n    --color-slate-700: oklch(37.2% 0.044 257.287);\\n    --color-slate-800: oklch(27.9% 0.041 260.031);\\n    --color-gray-300: oklch(87.2% 0.01 258.338);\\n    --color-gray-400: oklch(70.7% 0.022 261.325);\\n    --color-gray-700: oklch(37.3% 0.034 259.733);\\n    --color-gray-900: oklch(21% 0.034 264.665);\\n    --color-black: #000;\\n    --color-white: #fff;\\n    --spacing: 0.25rem;\\n    --container-sm: 24rem;\\n    --container-md: 28rem;\\n    --text-xs: 0.75rem;\\n    --text-xs--line-height: calc(1 / 0.75);\\n    --text-sm: 0.875rem;\\n    --text-sm--line-height: calc(1.25 / 0.875);\\n    --text-base: 1rem;\\n    --text-base--line-height: calc(1.5 / 1);\\n    --text-lg: 1.125rem;\\n    --text-lg--line-height: calc(1.75 / 1.125);\\n    --text-2xl: 1.5rem;\\n    --text-2xl--line-height: calc(2 / 1.5);\\n    --font-weight-normal: 400;\\n    --font-weight-medium: 500;\\n    --font-weight-semibold: 600;\\n    --font-weight-extrabold: 800;\\n    --tracking-tight: -0.025em;\\n    --tracking-widest: 0.1em;\\n    --leading-tight: 1.25;\\n    --radius-xs: 0.125rem;\\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n    --default-transition-duration: 150ms;\\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n    --default-font-family: var(--font-geist-sans);\\n    --default-mono-font-family: var(--font-geist-mono);\\n  }\\n}\\n@layer base {\\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\\n    box-sizing: border-box;\\n    margin: 0;\\n    padding: 0;\\n    border: 0 solid;\\n  }\\n  html, :host {\\n    line-height: 1.5;\\n    -webkit-text-size-adjust: 100%;\\n    tab-size: 4;\\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\");\\n    font-feature-settings: var(--default-font-feature-settings, normal);\\n    font-variation-settings: var(--default-font-variation-settings, normal);\\n    -webkit-tap-highlight-color: transparent;\\n  }\\n  hr {\\n    height: 0;\\n    color: inherit;\\n    border-top-width: 1px;\\n  }\\n  abbr:where([title]) {\\n    -webkit-text-decoration: underline dotted;\\n    text-decoration: underline dotted;\\n  }\\n  h1, h2, h3, h4, h5, h6 {\\n    font-size: inherit;\\n    font-weight: inherit;\\n  }\\n  a {\\n    color: inherit;\\n    -webkit-text-decoration: inherit;\\n    text-decoration: inherit;\\n  }\\n  b, strong {\\n    font-weight: bolder;\\n  }\\n  code, kbd, samp, pre {\\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace);\\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\\n    font-size: 1em;\\n  }\\n  small {\\n    font-size: 80%;\\n  }\\n  sub, sup {\\n    font-size: 75%;\\n    line-height: 0;\\n    position: relative;\\n    vertical-align: baseline;\\n  }\\n  sub {\\n    bottom: -0.25em;\\n  }\\n  sup {\\n    top: -0.5em;\\n  }\\n  table {\\n    text-indent: 0;\\n    border-color: inherit;\\n    border-collapse: collapse;\\n  }\\n  :-moz-focusring {\\n    outline: auto;\\n  }\\n  progress {\\n    vertical-align: baseline;\\n  }\\n  summary {\\n    display: list-item;\\n  }\\n  ol, ul, menu {\\n    list-style: none;\\n  }\\n  img, svg, video, canvas, audio, iframe, embed, object {\\n    display: block;\\n    vertical-align: middle;\\n  }\\n  img, video {\\n    max-width: 100%;\\n    height: auto;\\n  }\\n  button, input, select, optgroup, textarea, ::file-selector-button {\\n    font: inherit;\\n    font-feature-settings: inherit;\\n    font-variation-settings: inherit;\\n    letter-spacing: inherit;\\n    color: inherit;\\n    border-radius: 0;\\n    background-color: transparent;\\n    opacity: 1;\\n  }\\n  :where(select:is([multiple], [size])) optgroup {\\n    font-weight: bolder;\\n  }\\n  :where(select:is([multiple], [size])) optgroup option {\\n    padding-inline-start: 20px;\\n  }\\n  ::file-selector-button {\\n    margin-inline-end: 4px;\\n  }\\n  ::placeholder {\\n    opacity: 1;\\n  }\\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\\n    ::placeholder {\\n      color: currentcolor;\\n      @supports (color: color-mix(in lab, red, red)) {\\n        color: color-mix(in oklab, currentcolor 50%, transparent);\\n      }\\n    }\\n  }\\n  textarea {\\n    resize: vertical;\\n  }\\n  ::-webkit-search-decoration {\\n    -webkit-appearance: none;\\n  }\\n  ::-webkit-date-and-time-value {\\n    min-height: 1lh;\\n    text-align: inherit;\\n  }\\n  ::-webkit-datetime-edit {\\n    display: inline-flex;\\n  }\\n  ::-webkit-datetime-edit-fields-wrapper {\\n    padding: 0;\\n  }\\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\\n    padding-block: 0;\\n  }\\n  :-moz-ui-invalid {\\n    box-shadow: none;\\n  }\\n  button, input:where([type=\\\"button\\\"], [type=\\\"reset\\\"], [type=\\\"submit\\\"]), ::file-selector-button {\\n    appearance: button;\\n  }\\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\\n    height: auto;\\n  }\\n  [hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n    display: none !important;\\n  }\\n}\\n@layer utilities {\\n  .\\\\@container\\\\/card-header {\\n    container-type: inline-size;\\n    container-name: card-header;\\n  }\\n  .pointer-events-none {\\n    pointer-events: none;\\n  }\\n  .sr-only {\\n    position: absolute;\\n    width: 1px;\\n    height: 1px;\\n    padding: 0;\\n    margin: -1px;\\n    overflow: hidden;\\n    clip: rect(0, 0, 0, 0);\\n    white-space: nowrap;\\n    border-width: 0;\\n  }\\n  .absolute {\\n    position: absolute;\\n  }\\n  .fixed {\\n    position: fixed;\\n  }\\n  .relative {\\n    position: relative;\\n  }\\n  .inset-0 {\\n    inset: calc(var(--spacing) * 0);\\n  }\\n  .inset-x-0 {\\n    inset-inline: calc(var(--spacing) * 0);\\n  }\\n  .inset-y-0 {\\n    inset-block: calc(var(--spacing) * 0);\\n  }\\n  .-top-12 {\\n    top: calc(var(--spacing) * -12);\\n  }\\n  .top-0 {\\n    top: calc(var(--spacing) * 0);\\n  }\\n  .top-0\\\\.5 {\\n    top: calc(var(--spacing) * 0.5);\\n  }\\n  .top-1\\\\.5 {\\n    top: calc(var(--spacing) * 1.5);\\n  }\\n  .top-1\\\\/2 {\\n    top: calc(1/2 * 100%);\\n  }\\n  .top-2 {\\n    top: calc(var(--spacing) * 2);\\n  }\\n  .top-3\\\\.5 {\\n    top: calc(var(--spacing) * 3.5);\\n  }\\n  .top-4 {\\n    top: calc(var(--spacing) * 4);\\n  }\\n  .-right-12 {\\n    right: calc(var(--spacing) * -12);\\n  }\\n  .right-0 {\\n    right: calc(var(--spacing) * 0);\\n  }\\n  .right-0\\\\.5 {\\n    right: calc(var(--spacing) * 0.5);\\n  }\\n  .right-1 {\\n    right: calc(var(--spacing) * 1);\\n  }\\n  .right-2 {\\n    right: calc(var(--spacing) * 2);\\n  }\\n  .right-3 {\\n    right: calc(var(--spacing) * 3);\\n  }\\n  .right-4 {\\n    right: calc(var(--spacing) * 4);\\n  }\\n  .-bottom-12 {\\n    bottom: calc(var(--spacing) * -12);\\n  }\\n  .bottom-0 {\\n    bottom: calc(var(--spacing) * 0);\\n  }\\n  .-left-12 {\\n    left: calc(var(--spacing) * -12);\\n  }\\n  .left-0 {\\n    left: calc(var(--spacing) * 0);\\n  }\\n  .left-1\\\\/2 {\\n    left: calc(1/2 * 100%);\\n  }\\n  .left-2 {\\n    left: calc(var(--spacing) * 2);\\n  }\\n  .z-10 {\\n    z-index: 10;\\n  }\\n  .z-20 {\\n    z-index: 20;\\n  }\\n  .z-50 {\\n    z-index: 50;\\n  }\\n  .col-start-2 {\\n    grid-column-start: 2;\\n  }\\n  .row-span-2 {\\n    grid-row: span 2 / span 2;\\n  }\\n  .row-start-1 {\\n    grid-row-start: 1;\\n  }\\n  .m-6 {\\n    margin: calc(var(--spacing) * 6);\\n  }\\n  .m-auto {\\n    margin: auto;\\n  }\\n  .-mx-1 {\\n    margin-inline: calc(var(--spacing) * -1);\\n  }\\n  .mx-2 {\\n    margin-inline: calc(var(--spacing) * 2);\\n  }\\n  .mx-3\\\\.5 {\\n    margin-inline: calc(var(--spacing) * 3.5);\\n  }\\n  .mx-auto {\\n    margin-inline: auto;\\n  }\\n  .my-1 {\\n    margin-block: calc(var(--spacing) * 1);\\n  }\\n  .-mt-4 {\\n    margin-top: calc(var(--spacing) * -4);\\n  }\\n  .mt-0 {\\n    margin-top: calc(var(--spacing) * 0);\\n  }\\n  .mt-1 {\\n    margin-top: calc(var(--spacing) * 1);\\n  }\\n  .mt-4 {\\n    margin-top: calc(var(--spacing) * 4);\\n  }\\n  .mt-6 {\\n    margin-top: calc(var(--spacing) * 6);\\n  }\\n  .mt-8 {\\n    margin-top: calc(var(--spacing) * 8);\\n  }\\n  .mt-10 {\\n    margin-top: calc(var(--spacing) * 10);\\n  }\\n  .mt-auto {\\n    margin-top: auto;\\n  }\\n  .mb-2 {\\n    margin-bottom: calc(var(--spacing) * 2);\\n  }\\n  .mb-6 {\\n    margin-bottom: calc(var(--spacing) * 6);\\n  }\\n  .mb-10 {\\n    margin-bottom: calc(var(--spacing) * 10);\\n  }\\n  .-ml-4 {\\n    margin-left: calc(var(--spacing) * -4);\\n  }\\n  .ml-1 {\\n    margin-left: calc(var(--spacing) * 1);\\n  }\\n  .ml-6 {\\n    margin-left: calc(var(--spacing) * 6);\\n  }\\n  .ml-auto {\\n    margin-left: auto;\\n  }\\n  .block {\\n    display: block;\\n  }\\n  .flex {\\n    display: flex;\\n  }\\n  .grid {\\n    display: grid;\\n  }\\n  .hidden {\\n    display: none;\\n  }\\n  .inline-flex {\\n    display: inline-flex;\\n  }\\n  .table {\\n    display: table;\\n  }\\n  .table-caption {\\n    display: table-caption;\\n  }\\n  .table-cell {\\n    display: table-cell;\\n  }\\n  .table-row {\\n    display: table-row;\\n  }\\n  .aspect-square {\\n    aspect-ratio: 1 / 1;\\n  }\\n  .size-2 {\\n    width: calc(var(--spacing) * 2);\\n    height: calc(var(--spacing) * 2);\\n  }\\n  .size-2\\\\.5 {\\n    width: calc(var(--spacing) * 2.5);\\n    height: calc(var(--spacing) * 2.5);\\n  }\\n  .size-3\\\\.5 {\\n    width: calc(var(--spacing) * 3.5);\\n    height: calc(var(--spacing) * 3.5);\\n  }\\n  .size-4 {\\n    width: calc(var(--spacing) * 4);\\n    height: calc(var(--spacing) * 4);\\n  }\\n  .size-6 {\\n    width: calc(var(--spacing) * 6);\\n    height: calc(var(--spacing) * 6);\\n  }\\n  .size-7 {\\n    width: calc(var(--spacing) * 7);\\n    height: calc(var(--spacing) * 7);\\n  }\\n  .size-8 {\\n    width: calc(var(--spacing) * 8);\\n    height: calc(var(--spacing) * 8);\\n  }\\n  .size-9 {\\n    width: calc(var(--spacing) * 9);\\n    height: calc(var(--spacing) * 9);\\n  }\\n  .size-full {\\n    width: 100%;\\n    height: 100%;\\n  }\\n  .h-4 {\\n    height: calc(var(--spacing) * 4);\\n  }\\n  .h-5 {\\n    height: calc(var(--spacing) * 5);\\n  }\\n  .h-7 {\\n    height: calc(var(--spacing) * 7);\\n  }\\n  .h-8 {\\n    height: calc(var(--spacing) * 8);\\n  }\\n  .h-9 {\\n    height: calc(var(--spacing) * 9);\\n  }\\n  .h-10 {\\n    height: calc(var(--spacing) * 10);\\n  }\\n  .h-12 {\\n    height: calc(var(--spacing) * 12);\\n  }\\n  .h-16 {\\n    height: calc(var(--spacing) * 16);\\n  }\\n  .h-\\\\[calc\\\\(100\\\\%-1px\\\\)\\\\] {\\n    height: calc(100% - 1px);\\n  }\\n  .h-\\\\[var\\\\(--radix-select-trigger-height\\\\)\\\\] {\\n    height: var(--radix-select-trigger-height);\\n  }\\n  .h-auto {\\n    height: auto;\\n  }\\n  .h-full {\\n    height: 100%;\\n  }\\n  .h-px {\\n    height: 1px;\\n  }\\n  .h-screen {\\n    height: 100vh;\\n  }\\n  .h-svh {\\n    height: 100svh;\\n  }\\n  .max-h-\\\\(--radix-dropdown-menu-content-available-height\\\\) {\\n    max-height: var(--radix-dropdown-menu-content-available-height);\\n  }\\n  .max-h-\\\\(--radix-select-content-available-height\\\\) {\\n    max-height: var(--radix-select-content-available-height);\\n  }\\n  .min-h-0 {\\n    min-height: calc(var(--spacing) * 0);\\n  }\\n  .min-h-svh {\\n    min-height: 100svh;\\n  }\\n  .w-\\\\(--sidebar-width\\\\) {\\n    width: var(--sidebar-width);\\n  }\\n  .w-3\\\\/4 {\\n    width: calc(3/4 * 100%);\\n  }\\n  .w-4 {\\n    width: calc(var(--spacing) * 4);\\n  }\\n  .w-5 {\\n    width: calc(var(--spacing) * 5);\\n  }\\n  .w-8 {\\n    width: calc(var(--spacing) * 8);\\n  }\\n  .w-28 {\\n    width: calc(var(--spacing) * 28);\\n  }\\n  .w-42 {\\n    width: calc(var(--spacing) * 42);\\n  }\\n  .w-48 {\\n    width: calc(var(--spacing) * 48);\\n  }\\n  .w-52 {\\n    width: calc(var(--spacing) * 52);\\n  }\\n  .w-64 {\\n    width: calc(var(--spacing) * 64);\\n  }\\n  .w-72 {\\n    width: calc(var(--spacing) * 72);\\n  }\\n  .w-\\\\[--radix-dropdown-menu-trigger-width\\\\] {\\n    width: --radix-dropdown-menu-trigger-width;\\n  }\\n  .w-\\\\[220px\\\\] {\\n    width: 220px;\\n  }\\n  .w-auto {\\n    width: auto;\\n  }\\n  .w-fit {\\n    width: fit-content;\\n  }\\n  .w-full {\\n    width: 100%;\\n  }\\n  .max-w-\\\\(--skeleton-width\\\\) {\\n    max-width: var(--skeleton-width);\\n  }\\n  .max-w-sm {\\n    max-width: var(--container-sm);\\n  }\\n  .min-w-0 {\\n    min-width: calc(var(--spacing) * 0);\\n  }\\n  .min-w-5 {\\n    min-width: calc(var(--spacing) * 5);\\n  }\\n  .min-w-8 {\\n    min-width: calc(var(--spacing) * 8);\\n  }\\n  .min-w-56 {\\n    min-width: calc(var(--spacing) * 56);\\n  }\\n  .min-w-\\\\[8rem\\\\] {\\n    min-width: 8rem;\\n  }\\n  .min-w-\\\\[12rem\\\\] {\\n    min-width: 12rem;\\n  }\\n  .min-w-\\\\[20px\\\\] {\\n    min-width: 20px;\\n  }\\n  .min-w-\\\\[var\\\\(--radix-select-trigger-width\\\\)\\\\] {\\n    min-width: var(--radix-select-trigger-width);\\n  }\\n  .flex-1 {\\n    flex: 1;\\n  }\\n  .shrink-0 {\\n    flex-shrink: 0;\\n  }\\n  .grow-0 {\\n    flex-grow: 0;\\n  }\\n  .basis-full {\\n    flex-basis: 100%;\\n  }\\n  .caption-bottom {\\n    caption-side: bottom;\\n  }\\n  .origin-\\\\(--radix-dropdown-menu-content-transform-origin\\\\) {\\n    transform-origin: var(--radix-dropdown-menu-content-transform-origin);\\n  }\\n  .origin-\\\\(--radix-hover-card-content-transform-origin\\\\) {\\n    transform-origin: var(--radix-hover-card-content-transform-origin);\\n  }\\n  .origin-\\\\(--radix-menubar-content-transform-origin\\\\) {\\n    transform-origin: var(--radix-menubar-content-transform-origin);\\n  }\\n  .origin-\\\\(--radix-popover-content-transform-origin\\\\) {\\n    transform-origin: var(--radix-popover-content-transform-origin);\\n  }\\n  .origin-\\\\(--radix-select-content-transform-origin\\\\) {\\n    transform-origin: var(--radix-select-content-transform-origin);\\n  }\\n  .origin-\\\\(--radix-tooltip-content-transform-origin\\\\) {\\n    transform-origin: var(--radix-tooltip-content-transform-origin);\\n  }\\n  .-translate-x-1\\\\/2 {\\n    --tw-translate-x: calc(calc(1/2 * 100%) * -1);\\n    translate: var(--tw-translate-x) var(--tw-translate-y);\\n  }\\n  .-translate-x-px {\\n    --tw-translate-x: -1px;\\n    translate: var(--tw-translate-x) var(--tw-translate-y);\\n  }\\n  .translate-x-px {\\n    --tw-translate-x: 1px;\\n    translate: var(--tw-translate-x) var(--tw-translate-y);\\n  }\\n  .-translate-y-1\\\\/2 {\\n    --tw-translate-y: calc(calc(1/2 * 100%) * -1);\\n    translate: var(--tw-translate-x) var(--tw-translate-y);\\n  }\\n  .translate-y-0\\\\.5 {\\n    --tw-translate-y: calc(var(--spacing) * 0.5);\\n    translate: var(--tw-translate-x) var(--tw-translate-y);\\n  }\\n  .translate-y-\\\\[calc\\\\(-50\\\\%_-_2px\\\\)\\\\] {\\n    --tw-translate-y: calc(-50% - 2px);\\n    translate: var(--tw-translate-x) var(--tw-translate-y);\\n  }\\n  .rotate-45 {\\n    rotate: 45deg;\\n  }\\n  .rotate-90 {\\n    rotate: 90deg;\\n  }\\n  .transform {\\n    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\\n  }\\n  .animate-in {\\n    animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\\n  }\\n  .animate-pulse {\\n    animation: var(--animate-pulse);\\n  }\\n  .cursor-default {\\n    cursor: default;\\n  }\\n  .scroll-my-1 {\\n    scroll-margin-block: calc(var(--spacing) * 1);\\n  }\\n  .appearance-none {\\n    appearance: none;\\n  }\\n  .auto-rows-min {\\n    grid-auto-rows: min-content;\\n  }\\n  .grid-rows-\\\\[auto_auto\\\\] {\\n    grid-template-rows: auto auto;\\n  }\\n  .flex-col {\\n    flex-direction: column;\\n  }\\n  .flex-row {\\n    flex-direction: row;\\n  }\\n  .flex-wrap {\\n    flex-wrap: wrap;\\n  }\\n  .place-items-center {\\n    place-items: center;\\n  }\\n  .items-center {\\n    align-items: center;\\n  }\\n  .items-start {\\n    align-items: flex-start;\\n  }\\n  .justify-between {\\n    justify-content: space-between;\\n  }\\n  .justify-center {\\n    justify-content: center;\\n  }\\n  .gap-0 {\\n    gap: calc(var(--spacing) * 0);\\n  }\\n  .gap-1 {\\n    gap: calc(var(--spacing) * 1);\\n  }\\n  .gap-1\\\\.5 {\\n    gap: calc(var(--spacing) * 1.5);\\n  }\\n  .gap-2 {\\n    gap: calc(var(--spacing) * 2);\\n  }\\n  .gap-3 {\\n    gap: calc(var(--spacing) * 3);\\n  }\\n  .gap-4 {\\n    gap: calc(var(--spacing) * 4);\\n  }\\n  .gap-6 {\\n    gap: calc(var(--spacing) * 6);\\n  }\\n  .space-x-2 {\\n    :where(& > :not(:last-child)) {\\n      --tw-space-x-reverse: 0;\\n      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));\\n      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));\\n    }\\n  }\\n  .self-start {\\n    align-self: flex-start;\\n  }\\n  .justify-self-end {\\n    justify-self: flex-end;\\n  }\\n  .truncate {\\n    overflow: hidden;\\n    text-overflow: ellipsis;\\n    white-space: nowrap;\\n  }\\n  .overflow-auto {\\n    overflow: auto;\\n  }\\n  .overflow-hidden {\\n    overflow: hidden;\\n  }\\n  .overflow-x-auto {\\n    overflow-x: auto;\\n  }\\n  .overflow-x-hidden {\\n    overflow-x: hidden;\\n  }\\n  .overflow-y-auto {\\n    overflow-y: auto;\\n  }\\n  .rounded {\\n    border-radius: 0.25rem;\\n  }\\n  .rounded-\\\\[2px\\\\] {\\n    border-radius: 2px;\\n  }\\n  .rounded-full {\\n    border-radius: calc(infinity * 1px);\\n  }\\n  .rounded-lg {\\n    border-radius: var(--radius);\\n  }\\n  .rounded-md {\\n    border-radius: calc(var(--radius) - 2px);\\n  }\\n  .rounded-sm {\\n    border-radius: calc(var(--radius) - 4px);\\n  }\\n  .rounded-xl {\\n    border-radius: calc(var(--radius) + 4px);\\n  }\\n  .rounded-xs {\\n    border-radius: var(--radius-xs);\\n  }\\n  .rounded-l-full {\\n    border-top-left-radius: calc(infinity * 1px);\\n    border-bottom-left-radius: calc(infinity * 1px);\\n  }\\n  .border {\\n    border-style: var(--tw-border-style);\\n    border-width: 1px;\\n  }\\n  .border-t {\\n    border-top-style: var(--tw-border-style);\\n    border-top-width: 1px;\\n  }\\n  .border-r {\\n    border-right-style: var(--tw-border-style);\\n    border-right-width: 1px;\\n  }\\n  .border-b {\\n    border-bottom-style: var(--tw-border-style);\\n    border-bottom-width: 1px;\\n  }\\n  .border-l {\\n    border-left-style: var(--tw-border-style);\\n    border-left-width: 1px;\\n  }\\n  .border-gray-300 {\\n    border-color: var(--color-gray-300);\\n  }\\n  .border-input {\\n    border-color: var(--input);\\n  }\\n  .border-sidebar-border {\\n    border-color: var(--sidebar-border);\\n  }\\n  .border-slate-200 {\\n    border-color: var(--color-slate-200);\\n  }\\n  .border-transparent {\\n    border-color: transparent;\\n  }\\n  .bg-\\\\[\\\\#505253\\\\] {\\n    background-color: #505253;\\n  }\\n  .bg-accent {\\n    background-color: var(--accent);\\n  }\\n  .bg-amber-400 {\\n    background-color: var(--color-amber-400);\\n  }\\n  .bg-background {\\n    background-color: var(--background);\\n  }\\n  .bg-black\\\\/50 {\\n    background-color: color-mix(in srgb, #000 50%, transparent);\\n    @supports (color: color-mix(in lab, red, red)) {\\n      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);\\n    }\\n  }\\n  .bg-border {\\n    background-color: var(--border);\\n  }\\n  .bg-card {\\n    background-color: var(--card);\\n  }\\n  .bg-dccgreen {\\n    background-color: var(--dccgreen);\\n  }\\n  .bg-dcclightblue {\\n    background-color: var(--dcclightblue);\\n  }\\n  .bg-destructive {\\n    background-color: var(--destructive);\\n  }\\n  .bg-muted {\\n    background-color: var(--muted);\\n  }\\n  .bg-muted\\\\/50 {\\n    background-color: var(--muted);\\n    @supports (color: color-mix(in lab, red, red)) {\\n      background-color: color-mix(in oklab, var(--muted) 50%, transparent);\\n    }\\n  }\\n  .bg-popover {\\n    background-color: var(--popover);\\n  }\\n  .bg-primary {\\n    background-color: var(--primary);\\n  }\\n  .bg-primary\\\\/20 {\\n    background-color: var(--primary);\\n    @supports (color: color-mix(in lab, red, red)) {\\n      background-color: color-mix(in oklab, var(--primary) 20%, transparent);\\n    }\\n  }\\n  .bg-secondary {\\n    background-color: var(--secondary);\\n  }\\n  .bg-sidebar {\\n    background-color: var(--sidebar);\\n  }\\n  .bg-sidebar-border {\\n    background-color: var(--sidebar-border);\\n  }\\n  .bg-sidebar-primary {\\n    background-color: var(--sidebar-primary);\\n  }\\n  .bg-slate-800 {\\n    background-color: var(--color-slate-800);\\n  }\\n  .bg-transparent {\\n    background-color: transparent;\\n  }\\n  .bg-white {\\n    background-color: var(--color-white);\\n  }\\n  .fill-current {\\n    fill: currentcolor;\\n  }\\n  .fill-primary {\\n    fill: var(--primary);\\n  }\\n  .p-0 {\\n    padding: calc(var(--spacing) * 0);\\n  }\\n  .p-1 {\\n    padding: calc(var(--spacing) * 1);\\n  }\\n  .p-2 {\\n    padding: calc(var(--spacing) * 2);\\n  }\\n  .p-3 {\\n    padding: calc(var(--spacing) * 3);\\n  }\\n  .p-4 {\\n    padding: calc(var(--spacing) * 4);\\n  }\\n  .p-6 {\\n    padding: calc(var(--spacing) * 6);\\n  }\\n  .p-\\\\[3px\\\\] {\\n    padding: 3px;\\n  }\\n  .px-1 {\\n    padding-inline: calc(var(--spacing) * 1);\\n  }\\n  .px-2 {\\n    padding-inline: calc(var(--spacing) * 2);\\n  }\\n  .px-2\\\\.5 {\\n    padding-inline: calc(var(--spacing) * 2.5);\\n  }\\n  .px-3 {\\n    padding-inline: calc(var(--spacing) * 3);\\n  }\\n  .px-4 {\\n    padding-inline: calc(var(--spacing) * 4);\\n  }\\n  .px-6 {\\n    padding-inline: calc(var(--spacing) * 6);\\n  }\\n  .py-0\\\\.5 {\\n    padding-block: calc(var(--spacing) * 0.5);\\n  }\\n  .py-1 {\\n    padding-block: calc(var(--spacing) * 1);\\n  }\\n  .py-1\\\\.5 {\\n    padding-block: calc(var(--spacing) * 1.5);\\n  }\\n  .py-2 {\\n    padding-block: calc(var(--spacing) * 2);\\n  }\\n  .py-3 {\\n    padding-block: calc(var(--spacing) * 3);\\n  }\\n  .py-4 {\\n    padding-block: calc(var(--spacing) * 4);\\n  }\\n  .py-6 {\\n    padding-block: calc(var(--spacing) * 6);\\n  }\\n  .pt-0 {\\n    padding-top: calc(var(--spacing) * 0);\\n  }\\n  .pt-1 {\\n    padding-top: calc(var(--spacing) * 1);\\n  }\\n  .pt-4 {\\n    padding-top: calc(var(--spacing) * 4);\\n  }\\n  .pt-6 {\\n    padding-top: calc(var(--spacing) * 6);\\n  }\\n  .pr-2 {\\n    padding-right: calc(var(--spacing) * 2);\\n  }\\n  .pr-8 {\\n    padding-right: calc(var(--spacing) * 8);\\n  }\\n  .pb-2 {\\n    padding-bottom: calc(var(--spacing) * 2);\\n  }\\n  .pb-4 {\\n    padding-bottom: calc(var(--spacing) * 4);\\n  }\\n  .pl-2 {\\n    padding-left: calc(var(--spacing) * 2);\\n  }\\n  .pl-2\\\\.5 {\\n    padding-left: calc(var(--spacing) * 2.5);\\n  }\\n  .pl-3 {\\n    padding-left: calc(var(--spacing) * 3);\\n  }\\n  .pl-4 {\\n    padding-left: calc(var(--spacing) * 4);\\n  }\\n  .pl-8 {\\n    padding-left: calc(var(--spacing) * 8);\\n  }\\n  .text-center {\\n    text-align: center;\\n  }\\n  .text-left {\\n    text-align: left;\\n  }\\n  .align-middle {\\n    vertical-align: middle;\\n  }\\n  .text-2xl {\\n    font-size: var(--text-2xl);\\n    line-height: var(--tw-leading, var(--text-2xl--line-height));\\n  }\\n  .text-base {\\n    font-size: var(--text-base);\\n    line-height: var(--tw-leading, var(--text-base--line-height));\\n  }\\n  .text-lg {\\n    font-size: var(--text-lg);\\n    line-height: var(--tw-leading, var(--text-lg--line-height));\\n  }\\n  .text-sm {\\n    font-size: var(--text-sm);\\n    line-height: var(--tw-leading, var(--text-sm--line-height));\\n  }\\n  .text-xs {\\n    font-size: var(--text-xs);\\n    line-height: var(--tw-leading, var(--text-xs--line-height));\\n  }\\n  .leading-none {\\n    --tw-leading: 1;\\n    line-height: 1;\\n  }\\n  .leading-tight {\\n    --tw-leading: var(--leading-tight);\\n    line-height: var(--leading-tight);\\n  }\\n  .font-extrabold {\\n    --tw-font-weight: var(--font-weight-extrabold);\\n    font-weight: var(--font-weight-extrabold);\\n  }\\n  .font-medium {\\n    --tw-font-weight: var(--font-weight-medium);\\n    font-weight: var(--font-weight-medium);\\n  }\\n  .font-normal {\\n    --tw-font-weight: var(--font-weight-normal);\\n    font-weight: var(--font-weight-normal);\\n  }\\n  .font-semibold {\\n    --tw-font-weight: var(--font-weight-semibold);\\n    font-weight: var(--font-weight-semibold);\\n  }\\n  .tracking-tight {\\n    --tw-tracking: var(--tracking-tight);\\n    letter-spacing: var(--tracking-tight);\\n  }\\n  .tracking-widest {\\n    --tw-tracking: var(--tracking-widest);\\n    letter-spacing: var(--tracking-widest);\\n  }\\n  .text-balance {\\n    text-wrap: balance;\\n  }\\n  .break-words {\\n    overflow-wrap: break-word;\\n  }\\n  .whitespace-nowrap {\\n    white-space: nowrap;\\n  }\\n  .text-card-foreground {\\n    color: var(--card-foreground);\\n  }\\n  .text-dccblue {\\n    color: var(--dccblue);\\n  }\\n  .text-foreground {\\n    color: var(--foreground);\\n  }\\n  .text-gray-700 {\\n    color: var(--color-gray-700);\\n  }\\n  .text-gray-900 {\\n    color: var(--color-gray-900);\\n  }\\n  .text-muted-foreground {\\n    color: var(--muted-foreground);\\n  }\\n  .text-popover-foreground {\\n    color: var(--popover-foreground);\\n  }\\n  .text-primary {\\n    color: var(--primary);\\n  }\\n  .text-primary-foreground {\\n    color: var(--primary-foreground);\\n  }\\n  .text-secondary-foreground {\\n    color: var(--secondary-foreground);\\n  }\\n  .text-sidebar-foreground {\\n    color: var(--sidebar-foreground);\\n  }\\n  .text-sidebar-foreground\\\\/70 {\\n    color: var(--sidebar-foreground);\\n    @supports (color: color-mix(in lab, red, red)) {\\n      color: color-mix(in oklab, var(--sidebar-foreground) 70%, transparent);\\n    }\\n  }\\n  .text-sidebar-primary-foreground {\\n    color: var(--sidebar-primary-foreground);\\n  }\\n  .text-slate-700 {\\n    color: var(--color-slate-700);\\n  }\\n  .text-white {\\n    color: var(--color-white);\\n  }\\n  .tabular-nums {\\n    --tw-numeric-spacing: tabular-nums;\\n    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);\\n  }\\n  .underline-offset-4 {\\n    text-underline-offset: 4px;\\n  }\\n  .antialiased {\\n    -webkit-font-smoothing: antialiased;\\n    -moz-osx-font-smoothing: grayscale;\\n  }\\n  .placeholder-gray-400 {\\n    &::placeholder {\\n      color: var(--color-gray-400);\\n    }\\n  }\\n  .opacity-50 {\\n    opacity: 50%;\\n  }\\n  .opacity-70 {\\n    opacity: 70%;\\n  }\\n  .shadow-\\\\[0_0_0_1px_hsl\\\\(var\\\\(--sidebar-border\\\\)\\\\)\\\\] {\\n    --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-border)));\\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  }\\n  .shadow-lg {\\n    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  }\\n  .shadow-md {\\n    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  }\\n  .shadow-none {\\n    --tw-shadow: 0 0 #0000;\\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  }\\n  .shadow-sm {\\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  }\\n  .shadow-xs {\\n    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));\\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  }\\n  .ring-sidebar-ring {\\n    --tw-ring-color: var(--sidebar-ring);\\n  }\\n  .ring-offset-background {\\n    --tw-ring-offset-color: var(--background);\\n  }\\n  .outline-hidden {\\n    --tw-outline-style: none;\\n    outline-style: none;\\n    @media (forced-colors: active) {\\n      outline: 2px solid transparent;\\n      outline-offset: 2px;\\n    }\\n  }\\n  .outline {\\n    outline-style: var(--tw-outline-style);\\n    outline-width: 1px;\\n  }\\n  .transition {\\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-\\\\[color\\\\,box-shadow\\\\] {\\n    transition-property: color,box-shadow;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-\\\\[left\\\\,right\\\\,width\\\\] {\\n    transition-property: left,right,width;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-\\\\[margin\\\\,opacity\\\\] {\\n    transition-property: margin,opacity;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-\\\\[width\\\\,height\\\\,padding\\\\] {\\n    transition-property: width,height,padding;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-\\\\[width\\\\] {\\n    transition-property: width;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-all {\\n    transition-property: all;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-colors {\\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-opacity {\\n    transition-property: opacity;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-transform {\\n    transition-property: transform, translate, scale, rotate;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .duration-200 {\\n    --tw-duration: 200ms;\\n    transition-duration: 200ms;\\n  }\\n  .duration-300 {\\n    --tw-duration: 300ms;\\n    transition-duration: 300ms;\\n  }\\n  .ease-in-out {\\n    --tw-ease: var(--ease-in-out);\\n    transition-timing-function: var(--ease-in-out);\\n  }\\n  .ease-linear {\\n    --tw-ease: linear;\\n    transition-timing-function: linear;\\n  }\\n  .fade-in-0 {\\n    --tw-enter-opacity: calc(0/100);\\n    --tw-enter-opacity: 0;\\n  }\\n  .outline-none {\\n    --tw-outline-style: none;\\n    outline-style: none;\\n  }\\n  .select-none {\\n    -webkit-user-select: none;\\n    user-select: none;\\n  }\\n  .zoom-in-95 {\\n    --tw-enter-scale: calc(95*1%);\\n    --tw-enter-scale: .95;\\n  }\\n  .group-focus-within\\\\/menu-item\\\\:opacity-100 {\\n    &:is(:where(.group\\\\/menu-item):focus-within *) {\\n      opacity: 100%;\\n    }\\n  }\\n  .group-hover\\\\/menu-item\\\\:opacity-100 {\\n    &:is(:where(.group\\\\/menu-item):hover *) {\\n      @media (hover: hover) {\\n        opacity: 100%;\\n      }\\n    }\\n  }\\n  .group-has-data-\\\\[sidebar\\\\=menu-action\\\\]\\\\/menu-item\\\\:pr-8 {\\n    &:is(:where(.group\\\\/menu-item):has(*[data-sidebar=\\\"menu-action\\\"]) *) {\\n      padding-right: calc(var(--spacing) * 8);\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:-mt-8 {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      margin-top: calc(var(--spacing) * -8);\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:hidden {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      display: none;\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:size-8\\\\! {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      width: calc(var(--spacing) * 8) !important;\\n      height: calc(var(--spacing) * 8) !important;\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:w-\\\\(--sidebar-width-icon\\\\) {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      width: var(--sidebar-width-icon);\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:w-\\\\[calc\\\\(var\\\\(--sidebar-width-icon\\\\)\\\\+\\\\(--spacing\\\\(4\\\\)\\\\)\\\\)\\\\] {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      width: calc(var(--sidebar-width-icon) + (calc(var(--spacing) * 4)));\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:w-\\\\[calc\\\\(var\\\\(--sidebar-width-icon\\\\)\\\\+\\\\(--spacing\\\\(4\\\\)\\\\)\\\\+2px\\\\)\\\\] {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      width: calc(var(--sidebar-width-icon) + (calc(var(--spacing) * 4)) + 2px);\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:overflow-hidden {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      overflow: hidden;\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:p-0\\\\! {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      padding: calc(var(--spacing) * 0) !important;\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:p-2\\\\! {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      padding: calc(var(--spacing) * 2) !important;\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:opacity-0 {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      opacity: 0%;\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:right-\\\\[calc\\\\(var\\\\(--sidebar-width\\\\)\\\\*-1\\\\)\\\\] {\\n    &:is(:where(.group)[data-collapsible=\\\"offcanvas\\\"] *) {\\n      right: calc(var(--sidebar-width) * -1);\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:left-\\\\[calc\\\\(var\\\\(--sidebar-width\\\\)\\\\*-1\\\\)\\\\] {\\n    &:is(:where(.group)[data-collapsible=\\\"offcanvas\\\"] *) {\\n      left: calc(var(--sidebar-width) * -1);\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:w-0 {\\n    &:is(:where(.group)[data-collapsible=\\\"offcanvas\\\"] *) {\\n      width: calc(var(--spacing) * 0);\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:translate-x-0 {\\n    &:is(:where(.group)[data-collapsible=\\\"offcanvas\\\"] *) {\\n      --tw-translate-x: calc(var(--spacing) * 0);\\n      translate: var(--tw-translate-x) var(--tw-translate-y);\\n    }\\n  }\\n  .group-data-\\\\[side\\\\=left\\\\]\\\\:-right-4 {\\n    &:is(:where(.group)[data-side=\\\"left\\\"] *) {\\n      right: calc(var(--spacing) * -4);\\n    }\\n  }\\n  .group-data-\\\\[side\\\\=left\\\\]\\\\:border-r {\\n    &:is(:where(.group)[data-side=\\\"left\\\"] *) {\\n      border-right-style: var(--tw-border-style);\\n      border-right-width: 1px;\\n    }\\n  }\\n  .group-data-\\\\[side\\\\=right\\\\]\\\\:left-0 {\\n    &:is(:where(.group)[data-side=\\\"right\\\"] *) {\\n      left: calc(var(--spacing) * 0);\\n    }\\n  }\\n  .group-data-\\\\[side\\\\=right\\\\]\\\\:rotate-180 {\\n    &:is(:where(.group)[data-side=\\\"right\\\"] *) {\\n      rotate: 180deg;\\n    }\\n  }\\n  .group-data-\\\\[side\\\\=right\\\\]\\\\:border-l {\\n    &:is(:where(.group)[data-side=\\\"right\\\"] *) {\\n      border-left-style: var(--tw-border-style);\\n      border-left-width: 1px;\\n    }\\n  }\\n  .group-data-\\\\[state\\\\=open\\\\]\\\\/collapsible\\\\:rotate-90 {\\n    &:is(:where(.group\\\\/collapsible)[data-state=\\\"open\\\"] *) {\\n      rotate: 90deg;\\n    }\\n  }\\n  .group-data-\\\\[variant\\\\=floating\\\\]\\\\:rounded-lg {\\n    &:is(:where(.group)[data-variant=\\\"floating\\\"] *) {\\n      border-radius: var(--radius);\\n    }\\n  }\\n  .group-data-\\\\[variant\\\\=floating\\\\]\\\\:border {\\n    &:is(:where(.group)[data-variant=\\\"floating\\\"] *) {\\n      border-style: var(--tw-border-style);\\n      border-width: 1px;\\n    }\\n  }\\n  .group-data-\\\\[variant\\\\=floating\\\\]\\\\:border-sidebar-border {\\n    &:is(:where(.group)[data-variant=\\\"floating\\\"] *) {\\n      border-color: var(--sidebar-border);\\n    }\\n  }\\n  .group-data-\\\\[variant\\\\=floating\\\\]\\\\:shadow-sm {\\n    &:is(:where(.group)[data-variant=\\\"floating\\\"] *) {\\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .peer-hover\\\\/menu-button\\\\:text-sidebar-accent-foreground {\\n    &:is(:where(.peer\\\\/menu-button):hover ~ *) {\\n      @media (hover: hover) {\\n        color: var(--sidebar-accent-foreground);\\n      }\\n    }\\n  }\\n  .peer-data-\\\\[active\\\\=true\\\\]\\\\/menu-button\\\\:text-sidebar-accent-foreground {\\n    &:is(:where(.peer\\\\/menu-button)[data-active=\\\"true\\\"] ~ *) {\\n      color: var(--sidebar-accent-foreground);\\n    }\\n  }\\n  .peer-data-\\\\[size\\\\=default\\\\]\\\\/menu-button\\\\:top-1\\\\.5 {\\n    &:is(:where(.peer\\\\/menu-button)[data-size=\\\"default\\\"] ~ *) {\\n      top: calc(var(--spacing) * 1.5);\\n    }\\n  }\\n  .peer-data-\\\\[size\\\\=lg\\\\]\\\\/menu-button\\\\:top-2\\\\.5 {\\n    &:is(:where(.peer\\\\/menu-button)[data-size=\\\"lg\\\"] ~ *) {\\n      top: calc(var(--spacing) * 2.5);\\n    }\\n  }\\n  .peer-data-\\\\[size\\\\=sm\\\\]\\\\/menu-button\\\\:top-1 {\\n    &:is(:where(.peer\\\\/menu-button)[data-size=\\\"sm\\\"] ~ *) {\\n      top: calc(var(--spacing) * 1);\\n    }\\n  }\\n  .selection\\\\:bg-primary {\\n    & *::selection {\\n      background-color: var(--primary);\\n    }\\n    &::selection {\\n      background-color: var(--primary);\\n    }\\n  }\\n  .selection\\\\:text-primary-foreground {\\n    & *::selection {\\n      color: var(--primary-foreground);\\n    }\\n    &::selection {\\n      color: var(--primary-foreground);\\n    }\\n  }\\n  .file\\\\:inline-flex {\\n    &::file-selector-button {\\n      display: inline-flex;\\n    }\\n  }\\n  .file\\\\:h-7 {\\n    &::file-selector-button {\\n      height: calc(var(--spacing) * 7);\\n    }\\n  }\\n  .file\\\\:border-0 {\\n    &::file-selector-button {\\n      border-style: var(--tw-border-style);\\n      border-width: 0px;\\n    }\\n  }\\n  .file\\\\:bg-transparent {\\n    &::file-selector-button {\\n      background-color: transparent;\\n    }\\n  }\\n  .file\\\\:text-sm {\\n    &::file-selector-button {\\n      font-size: var(--text-sm);\\n      line-height: var(--tw-leading, var(--text-sm--line-height));\\n    }\\n  }\\n  .file\\\\:font-medium {\\n    &::file-selector-button {\\n      --tw-font-weight: var(--font-weight-medium);\\n      font-weight: var(--font-weight-medium);\\n    }\\n  }\\n  .file\\\\:text-foreground {\\n    &::file-selector-button {\\n      color: var(--foreground);\\n    }\\n  }\\n  .placeholder\\\\:text-muted-foreground {\\n    &::placeholder {\\n      color: var(--muted-foreground);\\n    }\\n  }\\n  .placeholder\\\\:text-slate-400 {\\n    &::placeholder {\\n      color: var(--color-slate-400);\\n    }\\n  }\\n  .after\\\\:absolute {\\n    &::after {\\n      content: var(--tw-content);\\n      position: absolute;\\n    }\\n  }\\n  .after\\\\:-inset-2 {\\n    &::after {\\n      content: var(--tw-content);\\n      inset: calc(var(--spacing) * -2);\\n    }\\n  }\\n  .after\\\\:inset-y-0 {\\n    &::after {\\n      content: var(--tw-content);\\n      inset-block: calc(var(--spacing) * 0);\\n    }\\n  }\\n  .after\\\\:left-1\\\\/2 {\\n    &::after {\\n      content: var(--tw-content);\\n      left: calc(1/2 * 100%);\\n    }\\n  }\\n  .after\\\\:w-\\\\[2px\\\\] {\\n    &::after {\\n      content: var(--tw-content);\\n      width: 2px;\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:after\\\\:left-full {\\n    &:is(:where(.group)[data-collapsible=\\\"offcanvas\\\"] *) {\\n      &::after {\\n        content: var(--tw-content);\\n        left: 100%;\\n      }\\n    }\\n  }\\n  .last\\\\:border-b-0 {\\n    &:last-child {\\n      border-bottom-style: var(--tw-border-style);\\n      border-bottom-width: 0px;\\n    }\\n  }\\n  .hover\\\\:border-slate-300 {\\n    &:hover {\\n      @media (hover: hover) {\\n        border-color: var(--color-slate-300);\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-accent {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--accent);\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-destructive\\\\/90 {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--destructive);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          background-color: color-mix(in oklab, var(--destructive) 90%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-green-700 {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--color-green-700);\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-muted\\\\/50 {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--muted);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          background-color: color-mix(in oklab, var(--muted) 50%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-primary\\\\/90 {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--primary);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          background-color: color-mix(in oklab, var(--primary) 90%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-secondary\\\\/80 {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--secondary);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          background-color: color-mix(in oklab, var(--secondary) 80%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-sidebar-accent {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--sidebar-accent);\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-white\\\\/10 {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: color-mix(in srgb, #fff 10%, transparent);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          background-color: color-mix(in oklab, var(--color-white) 10%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .hover\\\\:text-accent-foreground {\\n    &:hover {\\n      @media (hover: hover) {\\n        color: var(--accent-foreground);\\n      }\\n    }\\n  }\\n  .hover\\\\:text-foreground {\\n    &:hover {\\n      @media (hover: hover) {\\n        color: var(--foreground);\\n      }\\n    }\\n  }\\n  .hover\\\\:text-sidebar-accent-foreground {\\n    &:hover {\\n      @media (hover: hover) {\\n        color: var(--sidebar-accent-foreground);\\n      }\\n    }\\n  }\\n  .hover\\\\:underline {\\n    &:hover {\\n      @media (hover: hover) {\\n        text-decoration-line: underline;\\n      }\\n    }\\n  }\\n  .hover\\\\:opacity-100 {\\n    &:hover {\\n      @media (hover: hover) {\\n        opacity: 100%;\\n      }\\n    }\\n  }\\n  .hover\\\\:shadow {\\n    &:hover {\\n      @media (hover: hover) {\\n        --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n      }\\n    }\\n  }\\n  .hover\\\\:shadow-\\\\[0_0_0_1px_hsl\\\\(var\\\\(--sidebar-accent\\\\)\\\\)\\\\] {\\n    &:hover {\\n      @media (hover: hover) {\\n        --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-accent)));\\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n      }\\n    }\\n  }\\n  .hover\\\\:group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:bg-sidebar {\\n    &:hover {\\n      @media (hover: hover) {\\n        &:is(:where(.group)[data-collapsible=\\\"offcanvas\\\"] *) {\\n          background-color: var(--sidebar);\\n        }\\n      }\\n    }\\n  }\\n  .hover\\\\:after\\\\:bg-sidebar-border {\\n    &:hover {\\n      @media (hover: hover) {\\n        &::after {\\n          content: var(--tw-content);\\n          background-color: var(--sidebar-border);\\n        }\\n      }\\n    }\\n  }\\n  .focus\\\\:border-primary {\\n    &:focus {\\n      border-color: var(--primary);\\n    }\\n  }\\n  .focus\\\\:border-slate-400 {\\n    &:focus {\\n      border-color: var(--color-slate-400);\\n    }\\n  }\\n  .focus\\\\:bg-accent {\\n    &:focus {\\n      background-color: var(--accent);\\n    }\\n  }\\n  .focus\\\\:bg-green-500 {\\n    &:focus {\\n      background-color: var(--color-green-500);\\n    }\\n  }\\n  .focus\\\\:text-accent-foreground {\\n    &:focus {\\n      color: var(--accent-foreground);\\n    }\\n  }\\n  .focus\\\\:shadow {\\n    &:focus {\\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .focus\\\\:shadow-none {\\n    &:focus {\\n      --tw-shadow: 0 0 #0000;\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .focus\\\\:ring-2 {\\n    &:focus {\\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .focus\\\\:ring-indigo-500 {\\n    &:focus {\\n      --tw-ring-color: var(--color-indigo-500);\\n    }\\n  }\\n  .focus\\\\:ring-primary {\\n    &:focus {\\n      --tw-ring-color: var(--primary);\\n    }\\n  }\\n  .focus\\\\:ring-ring {\\n    &:focus {\\n      --tw-ring-color: var(--ring);\\n    }\\n  }\\n  .focus\\\\:ring-offset-2 {\\n    &:focus {\\n      --tw-ring-offset-width: 2px;\\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n    }\\n  }\\n  .focus\\\\:outline-hidden {\\n    &:focus {\\n      --tw-outline-style: none;\\n      outline-style: none;\\n      @media (forced-colors: active) {\\n        outline: 2px solid transparent;\\n        outline-offset: 2px;\\n      }\\n    }\\n  }\\n  .focus\\\\:outline-none {\\n    &:focus {\\n      --tw-outline-style: none;\\n      outline-style: none;\\n    }\\n  }\\n  .focus-visible\\\\:border-ring {\\n    &:focus-visible {\\n      border-color: var(--ring);\\n    }\\n  }\\n  .focus-visible\\\\:ring-2 {\\n    &:focus-visible {\\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .focus-visible\\\\:ring-\\\\[3px\\\\] {\\n    &:focus-visible {\\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .focus-visible\\\\:ring-destructive\\\\/20 {\\n    &:focus-visible {\\n      --tw-ring-color: var(--destructive);\\n      @supports (color: color-mix(in lab, red, red)) {\\n        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);\\n      }\\n    }\\n  }\\n  .focus-visible\\\\:ring-ring\\\\/50 {\\n    &:focus-visible {\\n      --tw-ring-color: var(--ring);\\n      @supports (color: color-mix(in lab, red, red)) {\\n        --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);\\n      }\\n    }\\n  }\\n  .focus-visible\\\\:outline-1 {\\n    &:focus-visible {\\n      outline-style: var(--tw-outline-style);\\n      outline-width: 1px;\\n    }\\n  }\\n  .focus-visible\\\\:outline-ring {\\n    &:focus-visible {\\n      outline-color: var(--ring);\\n    }\\n  }\\n  .active\\\\:bg-green-700 {\\n    &:active {\\n      background-color: var(--color-green-700);\\n    }\\n  }\\n  .active\\\\:bg-sidebar-accent {\\n    &:active {\\n      background-color: var(--sidebar-accent);\\n    }\\n  }\\n  .active\\\\:bg-white\\\\/10 {\\n    &:active {\\n      background-color: color-mix(in srgb, #fff 10%, transparent);\\n      @supports (color: color-mix(in lab, red, red)) {\\n        background-color: color-mix(in oklab, var(--color-white) 10%, transparent);\\n      }\\n    }\\n  }\\n  .active\\\\:text-sidebar-accent-foreground {\\n    &:active {\\n      color: var(--sidebar-accent-foreground);\\n    }\\n  }\\n  .active\\\\:shadow-none {\\n    &:active {\\n      --tw-shadow: 0 0 #0000;\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .disabled\\\\:pointer-events-none {\\n    &:disabled {\\n      pointer-events: none;\\n    }\\n  }\\n  .disabled\\\\:cursor-not-allowed {\\n    &:disabled {\\n      cursor: not-allowed;\\n    }\\n  }\\n  .disabled\\\\:opacity-50 {\\n    &:disabled {\\n      opacity: 50%;\\n    }\\n  }\\n  .disabled\\\\:shadow-none {\\n    &:disabled {\\n      --tw-shadow: 0 0 #0000;\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .in-data-\\\\[side\\\\=left\\\\]\\\\:cursor-w-resize {\\n    :where(*[data-side=\\\"left\\\"]) & {\\n      cursor: w-resize;\\n    }\\n  }\\n  .in-data-\\\\[side\\\\=right\\\\]\\\\:cursor-e-resize {\\n    :where(*[data-side=\\\"right\\\"]) & {\\n      cursor: e-resize;\\n    }\\n  }\\n  .has-data-\\\\[slot\\\\=card-action\\\\]\\\\:grid-cols-\\\\[1fr_auto\\\\] {\\n    &:has(*[data-slot=\\\"card-action\\\"]) {\\n      grid-template-columns: 1fr auto;\\n    }\\n  }\\n  .has-data-\\\\[variant\\\\=inset\\\\]\\\\:bg-sidebar {\\n    &:has(*[data-variant=\\\"inset\\\"]) {\\n      background-color: var(--sidebar);\\n    }\\n  }\\n  .has-\\\\[\\\\>svg\\\\]\\\\:px-2\\\\.5 {\\n    &:has(>svg) {\\n      padding-inline: calc(var(--spacing) * 2.5);\\n    }\\n  }\\n  .has-\\\\[\\\\>svg\\\\]\\\\:px-3 {\\n    &:has(>svg) {\\n      padding-inline: calc(var(--spacing) * 3);\\n    }\\n  }\\n  .has-\\\\[\\\\>svg\\\\]\\\\:px-4 {\\n    &:has(>svg) {\\n      padding-inline: calc(var(--spacing) * 4);\\n    }\\n  }\\n  .aria-disabled\\\\:pointer-events-none {\\n    &[aria-disabled=\\\"true\\\"] {\\n      pointer-events: none;\\n    }\\n  }\\n  .aria-disabled\\\\:opacity-50 {\\n    &[aria-disabled=\\\"true\\\"] {\\n      opacity: 50%;\\n    }\\n  }\\n  .aria-invalid\\\\:border-destructive {\\n    &[aria-invalid=\\\"true\\\"] {\\n      border-color: var(--destructive);\\n    }\\n  }\\n  .aria-invalid\\\\:ring-destructive\\\\/20 {\\n    &[aria-invalid=\\\"true\\\"] {\\n      --tw-ring-color: var(--destructive);\\n      @supports (color: color-mix(in lab, red, red)) {\\n        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);\\n      }\\n    }\\n  }\\n  .data-\\\\[active\\\\=true\\\\]\\\\:bg-sidebar-accent {\\n    &[data-active=\\\"true\\\"] {\\n      background-color: var(--sidebar-accent);\\n    }\\n  }\\n  .data-\\\\[active\\\\=true\\\\]\\\\:font-medium {\\n    &[data-active=\\\"true\\\"] {\\n      --tw-font-weight: var(--font-weight-medium);\\n      font-weight: var(--font-weight-medium);\\n    }\\n  }\\n  .data-\\\\[active\\\\=true\\\\]\\\\:text-sidebar-accent-foreground {\\n    &[data-active=\\\"true\\\"] {\\n      color: var(--sidebar-accent-foreground);\\n    }\\n  }\\n  .data-\\\\[disabled\\\\]\\\\:pointer-events-none {\\n    &[data-disabled] {\\n      pointer-events: none;\\n    }\\n  }\\n  .data-\\\\[disabled\\\\]\\\\:opacity-50 {\\n    &[data-disabled] {\\n      opacity: 50%;\\n    }\\n  }\\n  .data-\\\\[inset\\\\]\\\\:pl-8 {\\n    &[data-inset] {\\n      padding-left: calc(var(--spacing) * 8);\\n    }\\n  }\\n  .data-\\\\[orientation\\\\=horizontal\\\\]\\\\:h-px {\\n    &[data-orientation=\\\"horizontal\\\"] {\\n      height: 1px;\\n    }\\n  }\\n  .data-\\\\[orientation\\\\=horizontal\\\\]\\\\:w-full {\\n    &[data-orientation=\\\"horizontal\\\"] {\\n      width: 100%;\\n    }\\n  }\\n  .data-\\\\[orientation\\\\=vertical\\\\]\\\\:h-full {\\n    &[data-orientation=\\\"vertical\\\"] {\\n      height: 100%;\\n    }\\n  }\\n  .data-\\\\[orientation\\\\=vertical\\\\]\\\\:w-px {\\n    &[data-orientation=\\\"vertical\\\"] {\\n      width: 1px;\\n    }\\n  }\\n  .data-\\\\[placeholder\\\\]\\\\:text-muted-foreground {\\n    &[data-placeholder] {\\n      color: var(--muted-foreground);\\n    }\\n  }\\n  .data-\\\\[side\\\\=bottom\\\\]\\\\:translate-y-1 {\\n    &[data-side=\\\"bottom\\\"] {\\n      --tw-translate-y: calc(var(--spacing) * 1);\\n      translate: var(--tw-translate-x) var(--tw-translate-y);\\n    }\\n  }\\n  .data-\\\\[side\\\\=bottom\\\\]\\\\:slide-in-from-top-2 {\\n    &[data-side=\\\"bottom\\\"] {\\n      --tw-enter-translate-y: calc(2*var(--spacing)*-1);\\n    }\\n  }\\n  .data-\\\\[side\\\\=left\\\\]\\\\:-translate-x-1 {\\n    &[data-side=\\\"left\\\"] {\\n      --tw-translate-x: calc(var(--spacing) * -1);\\n      translate: var(--tw-translate-x) var(--tw-translate-y);\\n    }\\n  }\\n  .data-\\\\[side\\\\=left\\\\]\\\\:slide-in-from-right-2 {\\n    &[data-side=\\\"left\\\"] {\\n      --tw-enter-translate-x: calc(2*var(--spacing));\\n    }\\n  }\\n  .data-\\\\[side\\\\=right\\\\]\\\\:translate-x-1 {\\n    &[data-side=\\\"right\\\"] {\\n      --tw-translate-x: calc(var(--spacing) * 1);\\n      translate: var(--tw-translate-x) var(--tw-translate-y);\\n    }\\n  }\\n  .data-\\\\[side\\\\=right\\\\]\\\\:slide-in-from-left-2 {\\n    &[data-side=\\\"right\\\"] {\\n      --tw-enter-translate-x: calc(2*var(--spacing)*-1);\\n    }\\n  }\\n  .data-\\\\[side\\\\=top\\\\]\\\\:-translate-y-1 {\\n    &[data-side=\\\"top\\\"] {\\n      --tw-translate-y: calc(var(--spacing) * -1);\\n      translate: var(--tw-translate-x) var(--tw-translate-y);\\n    }\\n  }\\n  .data-\\\\[side\\\\=top\\\\]\\\\:slide-in-from-bottom-2 {\\n    &[data-side=\\\"top\\\"] {\\n      --tw-enter-translate-y: calc(2*var(--spacing));\\n    }\\n  }\\n  .data-\\\\[size\\\\=default\\\\]\\\\:h-9 {\\n    &[data-size=\\\"default\\\"] {\\n      height: calc(var(--spacing) * 9);\\n    }\\n  }\\n  .data-\\\\[size\\\\=sm\\\\]\\\\:h-8 {\\n    &[data-size=\\\"sm\\\"] {\\n      height: calc(var(--spacing) * 8);\\n    }\\n  }\\n  .\\\\*\\\\:data-\\\\[slot\\\\=select-value\\\\]\\\\:line-clamp-1 {\\n    :is(& > *) {\\n      &[data-slot=\\\"select-value\\\"] {\\n        overflow: hidden;\\n        display: -webkit-box;\\n        -webkit-box-orient: vertical;\\n        -webkit-line-clamp: 1;\\n      }\\n    }\\n  }\\n  .\\\\*\\\\:data-\\\\[slot\\\\=select-value\\\\]\\\\:flex {\\n    :is(& > *) {\\n      &[data-slot=\\\"select-value\\\"] {\\n        display: flex;\\n      }\\n    }\\n  }\\n  .\\\\*\\\\:data-\\\\[slot\\\\=select-value\\\\]\\\\:items-center {\\n    :is(& > *) {\\n      &[data-slot=\\\"select-value\\\"] {\\n        align-items: center;\\n      }\\n    }\\n  }\\n  .\\\\*\\\\:data-\\\\[slot\\\\=select-value\\\\]\\\\:gap-2 {\\n    :is(& > *) {\\n      &[data-slot=\\\"select-value\\\"] {\\n        gap: calc(var(--spacing) * 2);\\n      }\\n    }\\n  }\\n  .data-\\\\[slot\\\\=sidebar-menu-button\\\\]\\\\:\\\\!p-1\\\\.5 {\\n    &[data-slot=\\\"sidebar-menu-button\\\"] {\\n      padding: calc(var(--spacing) * 1.5) !important;\\n    }\\n  }\\n  .data-\\\\[state\\\\=active\\\\]\\\\:shadow-sm {\\n    &[data-state=\\\"active\\\"] {\\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:animate-accordion-up {\\n    &[data-state=\\\"closed\\\"] {\\n      animation: accordion-up var(--tw-animation-duration,var(--tw-duration,.2s))ease-out;\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:animate-out {\\n    &[data-state=\\\"closed\\\"] {\\n      animation: exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:duration-300 {\\n    &[data-state=\\\"closed\\\"] {\\n      --tw-duration: 300ms;\\n      transition-duration: 300ms;\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:fade-out-0 {\\n    &[data-state=\\\"closed\\\"] {\\n      --tw-exit-opacity: calc(0/100);\\n      --tw-exit-opacity: 0;\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:zoom-out-95 {\\n    &[data-state=\\\"closed\\\"] {\\n      --tw-exit-scale: calc(95*1%);\\n      --tw-exit-scale: .95;\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-bottom {\\n    &[data-state=\\\"closed\\\"] {\\n      --tw-exit-translate-y: 100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-left {\\n    &[data-state=\\\"closed\\\"] {\\n      --tw-exit-translate-x: -100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-right {\\n    &[data-state=\\\"closed\\\"] {\\n      --tw-exit-translate-x: 100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-top {\\n    &[data-state=\\\"closed\\\"] {\\n      --tw-exit-translate-y: -100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:animate-accordion-down {\\n    &[data-state=\\\"open\\\"] {\\n      animation: accordion-down var(--tw-animation-duration,var(--tw-duration,.2s))ease-out;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:animate-in {\\n    &[data-state=\\\"open\\\"] {\\n      animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:bg-accent {\\n    &[data-state=\\\"open\\\"] {\\n      background-color: var(--accent);\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:bg-secondary {\\n    &[data-state=\\\"open\\\"] {\\n      background-color: var(--secondary);\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:bg-sidebar-accent {\\n    &[data-state=\\\"open\\\"] {\\n      background-color: var(--sidebar-accent);\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:text-accent-foreground {\\n    &[data-state=\\\"open\\\"] {\\n      color: var(--accent-foreground);\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:text-sidebar-accent-foreground {\\n    &[data-state=\\\"open\\\"] {\\n      color: var(--sidebar-accent-foreground);\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:opacity-100 {\\n    &[data-state=\\\"open\\\"] {\\n      opacity: 100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:duration-500 {\\n    &[data-state=\\\"open\\\"] {\\n      --tw-duration: 500ms;\\n      transition-duration: 500ms;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:fade-in-0 {\\n    &[data-state=\\\"open\\\"] {\\n      --tw-enter-opacity: calc(0/100);\\n      --tw-enter-opacity: 0;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:zoom-in-95 {\\n    &[data-state=\\\"open\\\"] {\\n      --tw-enter-scale: calc(95*1%);\\n      --tw-enter-scale: .95;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-bottom {\\n    &[data-state=\\\"open\\\"] {\\n      --tw-enter-translate-y: 100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-left {\\n    &[data-state=\\\"open\\\"] {\\n      --tw-enter-translate-x: -100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-right {\\n    &[data-state=\\\"open\\\"] {\\n      --tw-enter-translate-x: 100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-top {\\n    &[data-state=\\\"open\\\"] {\\n      --tw-enter-translate-y: -100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:hover\\\\:bg-sidebar-accent {\\n    &[data-state=\\\"open\\\"] {\\n      &:hover {\\n        @media (hover: hover) {\\n          background-color: var(--sidebar-accent);\\n        }\\n      }\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:hover\\\\:text-sidebar-accent-foreground {\\n    &[data-state=\\\"open\\\"] {\\n      &:hover {\\n        @media (hover: hover) {\\n          color: var(--sidebar-accent-foreground);\\n        }\\n      }\\n    }\\n  }\\n  .data-\\\\[state\\\\=selected\\\\]\\\\:bg-muted {\\n    &[data-state=\\\"selected\\\"] {\\n      background-color: var(--muted);\\n    }\\n  }\\n  .data-\\\\[variant\\\\=destructive\\\\]\\\\:text-destructive {\\n    &[data-variant=\\\"destructive\\\"] {\\n      color: var(--destructive);\\n    }\\n  }\\n  .data-\\\\[variant\\\\=destructive\\\\]\\\\:focus\\\\:bg-destructive\\\\/10 {\\n    &[data-variant=\\\"destructive\\\"] {\\n      &:focus {\\n        background-color: var(--destructive);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          background-color: color-mix(in oklab, var(--destructive) 10%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .data-\\\\[variant\\\\=destructive\\\\]\\\\:focus\\\\:text-destructive {\\n    &[data-variant=\\\"destructive\\\"] {\\n      &:focus {\\n        color: var(--destructive);\\n      }\\n    }\\n  }\\n  .sm\\\\:mx-auto {\\n    @media (width >= 40rem) {\\n      margin-inline: auto;\\n    }\\n  }\\n  .sm\\\\:block {\\n    @media (width >= 40rem) {\\n      display: block;\\n    }\\n  }\\n  .sm\\\\:flex {\\n    @media (width >= 40rem) {\\n      display: flex;\\n    }\\n  }\\n  .sm\\\\:w-full {\\n    @media (width >= 40rem) {\\n      width: 100%;\\n    }\\n  }\\n  .sm\\\\:max-w-md {\\n    @media (width >= 40rem) {\\n      max-width: var(--container-md);\\n    }\\n  }\\n  .sm\\\\:max-w-sm {\\n    @media (width >= 40rem) {\\n      max-width: var(--container-sm);\\n    }\\n  }\\n  .sm\\\\:gap-2\\\\.5 {\\n    @media (width >= 40rem) {\\n      gap: calc(var(--spacing) * 2.5);\\n    }\\n  }\\n  .sm\\\\:px-6 {\\n    @media (width >= 40rem) {\\n      padding-inline: calc(var(--spacing) * 6);\\n    }\\n  }\\n  .sm\\\\:pr-2\\\\.5 {\\n    @media (width >= 40rem) {\\n      padding-right: calc(var(--spacing) * 2.5);\\n    }\\n  }\\n  .sm\\\\:pl-2\\\\.5 {\\n    @media (width >= 40rem) {\\n      padding-left: calc(var(--spacing) * 2.5);\\n    }\\n  }\\n  .sm\\\\:text-sm {\\n    @media (width >= 40rem) {\\n      font-size: var(--text-sm);\\n      line-height: var(--tw-leading, var(--text-sm--line-height));\\n    }\\n  }\\n  .md\\\\:block {\\n    @media (width >= 48rem) {\\n      display: block;\\n    }\\n  }\\n  .md\\\\:flex {\\n    @media (width >= 48rem) {\\n      display: flex;\\n    }\\n  }\\n  .md\\\\:text-sm {\\n    @media (width >= 48rem) {\\n      font-size: var(--text-sm);\\n      line-height: var(--tw-leading, var(--text-sm--line-height));\\n    }\\n  }\\n  .md\\\\:opacity-0 {\\n    @media (width >= 48rem) {\\n      opacity: 0%;\\n    }\\n  }\\n  .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:m-2 {\\n    @media (width >= 48rem) {\\n      &:is(:where(.peer)[data-variant=\\\"inset\\\"] ~ *) {\\n        margin: calc(var(--spacing) * 2);\\n      }\\n    }\\n  }\\n  .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:ml-0 {\\n    @media (width >= 48rem) {\\n      &:is(:where(.peer)[data-variant=\\\"inset\\\"] ~ *) {\\n        margin-left: calc(var(--spacing) * 0);\\n      }\\n    }\\n  }\\n  .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:rounded-xl {\\n    @media (width >= 48rem) {\\n      &:is(:where(.peer)[data-variant=\\\"inset\\\"] ~ *) {\\n        border-radius: calc(var(--radius) + 4px);\\n      }\\n    }\\n  }\\n  .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:shadow-sm {\\n    @media (width >= 48rem) {\\n      &:is(:where(.peer)[data-variant=\\\"inset\\\"] ~ *) {\\n        --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n      }\\n    }\\n  }\\n  .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:peer-data-\\\\[state\\\\=collapsed\\\\]\\\\:ml-2 {\\n    @media (width >= 48rem) {\\n      &:is(:where(.peer)[data-variant=\\\"inset\\\"] ~ *) {\\n        &:is(:where(.peer)[data-state=\\\"collapsed\\\"] ~ *) {\\n          margin-left: calc(var(--spacing) * 2);\\n        }\\n      }\\n    }\\n  }\\n  .md\\\\:after\\\\:hidden {\\n    @media (width >= 48rem) {\\n      &::after {\\n        content: var(--tw-content);\\n        display: none;\\n      }\\n    }\\n  }\\n  .lg\\\\:px-8 {\\n    @media (width >= 64rem) {\\n      padding-inline: calc(var(--spacing) * 8);\\n    }\\n  }\\n  .dark\\\\:border-input {\\n    &:is(.dark *) {\\n      border-color: var(--input);\\n    }\\n  }\\n  .dark\\\\:bg-destructive\\\\/60 {\\n    &:is(.dark *) {\\n      background-color: var(--destructive);\\n      @supports (color: color-mix(in lab, red, red)) {\\n        background-color: color-mix(in oklab, var(--destructive) 60%, transparent);\\n      }\\n    }\\n  }\\n  .dark\\\\:bg-input\\\\/30 {\\n    &:is(.dark *) {\\n      background-color: var(--input);\\n      @supports (color: color-mix(in lab, red, red)) {\\n        background-color: color-mix(in oklab, var(--input) 30%, transparent);\\n      }\\n    }\\n  }\\n  .dark\\\\:text-muted-foreground {\\n    &:is(.dark *) {\\n      color: var(--muted-foreground);\\n    }\\n  }\\n  .dark\\\\:hover\\\\:bg-accent\\\\/50 {\\n    &:is(.dark *) {\\n      &:hover {\\n        @media (hover: hover) {\\n          background-color: var(--accent);\\n          @supports (color: color-mix(in lab, red, red)) {\\n            background-color: color-mix(in oklab, var(--accent) 50%, transparent);\\n          }\\n        }\\n      }\\n    }\\n  }\\n  .dark\\\\:hover\\\\:bg-input\\\\/50 {\\n    &:is(.dark *) {\\n      &:hover {\\n        @media (hover: hover) {\\n          background-color: var(--input);\\n          @supports (color: color-mix(in lab, red, red)) {\\n            background-color: color-mix(in oklab, var(--input) 50%, transparent);\\n          }\\n        }\\n      }\\n    }\\n  }\\n  .dark\\\\:focus-visible\\\\:ring-destructive\\\\/40 {\\n    &:is(.dark *) {\\n      &:focus-visible {\\n        --tw-ring-color: var(--destructive);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .dark\\\\:aria-invalid\\\\:ring-destructive\\\\/40 {\\n    &:is(.dark *) {\\n      &[aria-invalid=\\\"true\\\"] {\\n        --tw-ring-color: var(--destructive);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .dark\\\\:data-\\\\[state\\\\=active\\\\]\\\\:border-input {\\n    &:is(.dark *) {\\n      &[data-state=\\\"active\\\"] {\\n        border-color: var(--input);\\n      }\\n    }\\n  }\\n  .dark\\\\:data-\\\\[state\\\\=active\\\\]\\\\:bg-input\\\\/30 {\\n    &:is(.dark *) {\\n      &[data-state=\\\"active\\\"] {\\n        background-color: var(--input);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          background-color: color-mix(in oklab, var(--input) 30%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .dark\\\\:data-\\\\[state\\\\=active\\\\]\\\\:text-foreground {\\n    &:is(.dark *) {\\n      &[data-state=\\\"active\\\"] {\\n        color: var(--foreground);\\n      }\\n    }\\n  }\\n  .dark\\\\:data-\\\\[variant\\\\=destructive\\\\]\\\\:focus\\\\:bg-destructive\\\\/20 {\\n    &:is(.dark *) {\\n      &[data-variant=\\\"destructive\\\"] {\\n        &:focus {\\n          background-color: var(--destructive);\\n          @supports (color: color-mix(in lab, red, red)) {\\n            background-color: color-mix(in oklab, var(--destructive) 20%, transparent);\\n          }\\n        }\\n      }\\n    }\\n  }\\n  .\\\\[\\\\&_svg\\\\]\\\\:pointer-events-none {\\n    & svg {\\n      pointer-events: none;\\n    }\\n  }\\n  .\\\\[\\\\&_svg\\\\]\\\\:shrink-0 {\\n    & svg {\\n      flex-shrink: 0;\\n    }\\n  }\\n  .\\\\[\\\\&_svg\\\\:not\\\\(\\\\[class\\\\*\\\\=\\\\'size-\\\\'\\\\]\\\\)\\\\]\\\\:size-4 {\\n    & svg:not([class*='size-']) {\\n      width: calc(var(--spacing) * 4);\\n      height: calc(var(--spacing) * 4);\\n    }\\n  }\\n  .\\\\[\\\\&_svg\\\\:not\\\\(\\\\[class\\\\*\\\\=\\\\'text-\\\\'\\\\]\\\\)\\\\]\\\\:text-muted-foreground {\\n    & svg:not([class*='text-']) {\\n      color: var(--muted-foreground);\\n    }\\n  }\\n  .\\\\[\\\\&_tr\\\\]\\\\:border-b {\\n    & tr {\\n      border-bottom-style: var(--tw-border-style);\\n      border-bottom-width: 1px;\\n    }\\n  }\\n  .\\\\[\\\\&_tr\\\\:last-child\\\\]\\\\:border-0 {\\n    & tr:last-child {\\n      border-style: var(--tw-border-style);\\n      border-width: 0px;\\n    }\\n  }\\n  .\\\\[\\\\&\\\\:has\\\\(\\\\[role\\\\=checkbox\\\\]\\\\)\\\\]\\\\:pr-0 {\\n    &:has([role=checkbox]) {\\n      padding-right: calc(var(--spacing) * 0);\\n    }\\n  }\\n  .\\\\[\\\\.border-b\\\\]\\\\:pb-3 {\\n    &:is(.border-b) {\\n      padding-bottom: calc(var(--spacing) * 3);\\n    }\\n  }\\n  .\\\\[\\\\.border-t\\\\]\\\\:pt-6 {\\n    &:is(.border-t) {\\n      padding-top: calc(var(--spacing) * 6);\\n    }\\n  }\\n  .\\\\*\\\\:\\\\[span\\\\]\\\\:last\\\\:flex {\\n    :is(& > *) {\\n      &:is(span) {\\n        &:last-child {\\n          display: flex;\\n        }\\n      }\\n    }\\n  }\\n  .\\\\*\\\\:\\\\[span\\\\]\\\\:last\\\\:items-center {\\n    :is(& > *) {\\n      &:is(span) {\\n        &:last-child {\\n          align-items: center;\\n        }\\n      }\\n    }\\n  }\\n  .\\\\*\\\\:\\\\[span\\\\]\\\\:last\\\\:gap-2 {\\n    :is(& > *) {\\n      &:is(span) {\\n        &:last-child {\\n          gap: calc(var(--spacing) * 2);\\n        }\\n      }\\n    }\\n  }\\n  .data-\\\\[variant\\\\=destructive\\\\]\\\\:\\\\*\\\\:\\\\[svg\\\\]\\\\:\\\\!text-destructive {\\n    &[data-variant=\\\"destructive\\\"] {\\n      :is(& > *) {\\n        &:is(svg) {\\n          color: var(--destructive) !important;\\n        }\\n      }\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>\\\\[role\\\\=checkbox\\\\]\\\\]\\\\:translate-y-\\\\[2px\\\\] {\\n    &>[role=checkbox] {\\n      --tw-translate-y: 2px;\\n      translate: var(--tw-translate-x) var(--tw-translate-y);\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>button\\\\]\\\\:hidden {\\n    &>button {\\n      display: none;\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>span\\\\:last-child\\\\]\\\\:truncate {\\n    &>span:last-child {\\n      overflow: hidden;\\n      text-overflow: ellipsis;\\n      white-space: nowrap;\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>svg\\\\]\\\\:pointer-events-none {\\n    &>svg {\\n      pointer-events: none;\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>svg\\\\]\\\\:size-3 {\\n    &>svg {\\n      width: calc(var(--spacing) * 3);\\n      height: calc(var(--spacing) * 3);\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>svg\\\\]\\\\:size-3\\\\.5 {\\n    &>svg {\\n      width: calc(var(--spacing) * 3.5);\\n      height: calc(var(--spacing) * 3.5);\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>svg\\\\]\\\\:size-4 {\\n    &>svg {\\n      width: calc(var(--spacing) * 4);\\n      height: calc(var(--spacing) * 4);\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>svg\\\\]\\\\:shrink-0 {\\n    &>svg {\\n      flex-shrink: 0;\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>svg\\\\]\\\\:text-sidebar-accent-foreground {\\n    &>svg {\\n      color: var(--sidebar-accent-foreground);\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>tr\\\\]\\\\:last\\\\:border-b-0 {\\n    &>tr {\\n      &:last-child {\\n        border-bottom-style: var(--tw-border-style);\\n        border-bottom-width: 0px;\\n      }\\n    }\\n  }\\n  .\\\\[\\\\&\\\\[data-state\\\\=open\\\\]\\\\>svg\\\\]\\\\:rotate-180 {\\n    &[data-state=open]>svg {\\n      rotate: 180deg;\\n    }\\n  }\\n  .\\\\[\\\\[data-side\\\\=left\\\\]\\\\[data-collapsible\\\\=offcanvas\\\\]_\\\\&\\\\]\\\\:-right-2 {\\n    [data-side=left][data-collapsible=offcanvas] & {\\n      right: calc(var(--spacing) * -2);\\n    }\\n  }\\n  .\\\\[\\\\[data-side\\\\=left\\\\]\\\\[data-state\\\\=collapsed\\\\]_\\\\&\\\\]\\\\:cursor-e-resize {\\n    [data-side=left][data-state=collapsed] & {\\n      cursor: e-resize;\\n    }\\n  }\\n  .\\\\[\\\\[data-side\\\\=right\\\\]\\\\[data-collapsible\\\\=offcanvas\\\\]_\\\\&\\\\]\\\\:-left-2 {\\n    [data-side=right][data-collapsible=offcanvas] & {\\n      left: calc(var(--spacing) * -2);\\n    }\\n  }\\n  .\\\\[\\\\[data-side\\\\=right\\\\]\\\\[data-state\\\\=collapsed\\\\]_\\\\&\\\\]\\\\:cursor-w-resize {\\n    [data-side=right][data-state=collapsed] & {\\n      cursor: w-resize;\\n    }\\n  }\\n  .\\\\[a\\\\&\\\\]\\\\:hover\\\\:bg-accent {\\n    a& {\\n      &:hover {\\n        @media (hover: hover) {\\n          background-color: var(--accent);\\n        }\\n      }\\n    }\\n  }\\n  .\\\\[a\\\\&\\\\]\\\\:hover\\\\:bg-destructive\\\\/90 {\\n    a& {\\n      &:hover {\\n        @media (hover: hover) {\\n          background-color: var(--destructive);\\n          @supports (color: color-mix(in lab, red, red)) {\\n            background-color: color-mix(in oklab, var(--destructive) 90%, transparent);\\n          }\\n        }\\n      }\\n    }\\n  }\\n  .\\\\[a\\\\&\\\\]\\\\:hover\\\\:bg-primary\\\\/90 {\\n    a& {\\n      &:hover {\\n        @media (hover: hover) {\\n          background-color: var(--primary);\\n          @supports (color: color-mix(in lab, red, red)) {\\n            background-color: color-mix(in oklab, var(--primary) 90%, transparent);\\n          }\\n        }\\n      }\\n    }\\n  }\\n  .\\\\[a\\\\&\\\\]\\\\:hover\\\\:bg-secondary\\\\/90 {\\n    a& {\\n      &:hover {\\n        @media (hover: hover) {\\n          background-color: var(--secondary);\\n          @supports (color: color-mix(in lab, red, red)) {\\n            background-color: color-mix(in oklab, var(--secondary) 90%, transparent);\\n          }\\n        }\\n      }\\n    }\\n  }\\n  .\\\\[a\\\\&\\\\]\\\\:hover\\\\:text-accent-foreground {\\n    a& {\\n      &:hover {\\n        @media (hover: hover) {\\n          color: var(--accent-foreground);\\n        }\\n      }\\n    }\\n  }\\n}\\n@property --tw-animation-delay {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0s;\\n}\\n@property --tw-animation-direction {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: normal;\\n}\\n@property --tw-animation-duration {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-animation-fill-mode {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: none;\\n}\\n@property --tw-animation-iteration-count {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 1;\\n}\\n@property --tw-enter-opacity {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 1;\\n}\\n@property --tw-enter-rotate {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-enter-scale {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 1;\\n}\\n@property --tw-enter-translate-x {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-enter-translate-y {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-exit-opacity {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 1;\\n}\\n@property --tw-exit-rotate {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-exit-scale {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 1;\\n}\\n@property --tw-exit-translate-x {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-exit-translate-y {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n:root {\\n  --background: #ffffff;\\n  --foreground: #171717;\\n  --card: oklch(1 0 0);\\n  --card-foreground: oklch(0.145 0 0);\\n  --popover: oklch(1 0 0);\\n  --popover-foreground: oklch(0.145 0 0);\\n  --primary: oklch(24.37% 0.0951 286.37);\\n  --primary-foreground: oklch(0.985 0 0);\\n  --secondary: oklch(37.34% 0.1397 315.97);\\n  --secondary-foreground: oklch(0.205 0 0);\\n  --muted: oklch(0.97 0 0);\\n  --muted-foreground: oklch(0.556 0 0);\\n  --accent: oklch(0.97 0 0);\\n  --accent-foreground: oklch(0.205 0 0);\\n  --destructive: oklch(0.577 0.245 27.325);\\n  --destructive-foreground: oklch(0.577 0.245 27.325);\\n  --border: oklch(0.922 0 0);\\n  --input: oklch(0.922 0 0);\\n  --ring: oklch(0.708 0 0);\\n  --chart-1: oklch(0.646 0.222 41.116);\\n  --chart-2: oklch(0.6 0.118 184.704);\\n  --chart-3: oklch(0.398 0.07 227.392);\\n  --chart-4: oklch(0.828 0.189 84.429);\\n  --chart-5: oklch(0.769 0.188 70.08);\\n  --radius: 0.625rem;\\n  --sidebar: oklch(0.985 0 0);\\n  --sidebar-foreground: oklch(0.145 0 0);\\n  --sidebar-primary: oklch(0.205 0 0);\\n  --sidebar-primary-foreground: oklch(0.985 0 0);\\n  --sidebar-accent: oklch(0.97 0 0);\\n  --sidebar-accent-foreground: oklch(0.205 0 0);\\n  --sidebar-border: oklch(0.922 0 0);\\n  --sidebar-ring: oklch(0.708 0 0);\\n  --dccpink: oklch(0.54 0.216689 5.2);\\n  --dccblue: oklch(0.24 0.0951 286.37);\\n  --dcclightblue: oklch(0.64 0.1154 218.6);\\n  --dccviolet: oklch(0.45 0.1883 326.95);\\n  --dccpurple: oklch(0.37 0.1397 315.97);\\n  --dcclightgrey: oklch(0.7 0.0146 134.93);\\n  --dccdarkgrey: oklch(0.44 0.0031 228.84);\\n  --dccyellow: oklch(0.87 0.1768 90.38);\\n  --dccgreen: oklch(0.75 0.1806 124.9);\\n  --dccgrey: oklch(0.7 0.0146 134.93);\\n  --dccorange: oklch(0.75 0.1674 64.79);\\n  --dcclightorange: oklch(0.83 0.1464 73.9);\\n}\\nbody {\\n  background: var(--background);\\n  color: var(--foreground);\\n  font-family: Arial, Helvetica, sans-serif;\\n}\\n.dark {\\n  --background: oklch(0.145 0 0);\\n  --foreground: oklch(0.985 0 0);\\n  --card: oklch(0.145 0 0);\\n  --card-foreground: oklch(0.985 0 0);\\n  --popover: oklch(0.145 0 0);\\n  --popover-foreground: oklch(0.985 0 0);\\n  --primary: oklch(0.985 0 0);\\n  --primary-foreground: oklch(0.205 0 0);\\n  --secondary: oklch(0.269 0 0);\\n  --secondary-foreground: oklch(0.985 0 0);\\n  --muted: oklch(0.269 0 0);\\n  --muted-foreground: oklch(0.708 0 0);\\n  --accent: oklch(0.269 0 0);\\n  --accent-foreground: oklch(0.985 0 0);\\n  --destructive: oklch(0.396 0.141 25.723);\\n  --destructive-foreground: oklch(0.637 0.237 25.331);\\n  --border: oklch(0.269 0 0);\\n  --input: oklch(0.269 0 0);\\n  --ring: oklch(0.439 0 0);\\n  --chart-1: oklch(0.488 0.243 264.376);\\n  --chart-2: oklch(0.696 0.17 162.48);\\n  --chart-3: oklch(0.769 0.188 70.08);\\n  --chart-4: oklch(0.627 0.265 303.9);\\n  --chart-5: oklch(0.645 0.246 16.439);\\n  --sidebar: oklch(0.205 0 0);\\n  --sidebar-foreground: oklch(0.985 0 0);\\n  --sidebar-primary: oklch(0.488 0.243 264.376);\\n  --sidebar-primary-foreground: oklch(0.985 0 0);\\n  --sidebar-accent: oklch(0.269 0 0);\\n  --sidebar-accent-foreground: oklch(0.985 0 0);\\n  --sidebar-border: oklch(0.269 0 0);\\n  --sidebar-ring: oklch(0.439 0 0);\\n}\\n@layer base {\\n  * {\\n    border-color: var(--border);\\n    outline-color: var(--ring);\\n    @supports (color: color-mix(in lab, red, red)) {\\n      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);\\n    }\\n  }\\n  body {\\n    background-color: var(--background);\\n    color: var(--foreground);\\n  }\\n}\\n@layer base {\\n  :root {\\n    --sidebar-background: 0 0% 98%;\\n    --sidebar-foreground: 240 5.3% 26.1%;\\n    --sidebar-primary: 240 5.9% 10%;\\n    --sidebar-primary-foreground: 0 0% 98%;\\n    --sidebar-accent: 240 4.8% 95.9%;\\n    --sidebar-accent-foreground: 240 5.9% 10%;\\n    --sidebar-border: 220 13% 91%;\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\n  }\\n  .dark {\\n    --sidebar-background: 240 5.9% 10%;\\n    --sidebar-foreground: 240 4.8% 95.9%;\\n    --sidebar-primary: 224.3 76.3% 48%;\\n    --sidebar-primary-foreground: 0 0% 100%;\\n    --sidebar-accent: 240 3.7% 15.9%;\\n    --sidebar-accent-foreground: 240 4.8% 95.9%;\\n    --sidebar-border: 240 3.7% 15.9%;\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\n  }\\n}\\n@property --tw-translate-x {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-translate-y {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-translate-z {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-rotate-x {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-rotate-y {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-rotate-z {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-skew-x {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-skew-y {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-space-x-reverse {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-border-style {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: solid;\\n}\\n@property --tw-leading {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-font-weight {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-tracking {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-ordinal {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-slashed-zero {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-numeric-figure {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-numeric-spacing {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-numeric-fraction {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-shadow-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-shadow-alpha {\\n  syntax: \\\"<percentage>\\\";\\n  inherits: false;\\n  initial-value: 100%;\\n}\\n@property --tw-inset-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-inset-shadow-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-inset-shadow-alpha {\\n  syntax: \\\"<percentage>\\\";\\n  inherits: false;\\n  initial-value: 100%;\\n}\\n@property --tw-ring-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-ring-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-inset-ring-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-inset-ring-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-ring-inset {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-ring-offset-width {\\n  syntax: \\\"<length>\\\";\\n  inherits: false;\\n  initial-value: 0px;\\n}\\n@property --tw-ring-offset-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: #fff;\\n}\\n@property --tw-ring-offset-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-outline-style {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: solid;\\n}\\n@property --tw-duration {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-ease {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-content {\\n  syntax: \\\"*\\\";\\n  initial-value: \\\"\\\";\\n  inherits: false;\\n}\\n@keyframes pulse {\\n  50% {\\n    opacity: 0.5;\\n  }\\n}\\n@keyframes enter {\\n  from {\\n    opacity: var(--tw-enter-opacity,1);\\n    transform: translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0)scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1))rotate(var(--tw-enter-rotate,0));\\n  }\\n}\\n@keyframes exit {\\n  to {\\n    opacity: var(--tw-exit-opacity,1);\\n    transform: translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0)scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1))rotate(var(--tw-exit-rotate,0));\\n  }\\n}\\n@keyframes accordion-down {\\n  from {\\n    height: 0;\\n  }\\n  to {\\n    height: var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,var(--kb-accordion-content-height,auto))));\\n  }\\n}\\n@keyframes accordion-up {\\n  from {\\n    height: var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,var(--kb-accordion-content-height,auto))));\\n  }\\n  to {\\n    height: 0;\\n  }\\n}\\n@layer properties {\\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\\n    *, ::before, ::after, ::backdrop {\\n      --tw-translate-x: 0;\\n      --tw-translate-y: 0;\\n      --tw-translate-z: 0;\\n      --tw-rotate-x: initial;\\n      --tw-rotate-y: initial;\\n      --tw-rotate-z: initial;\\n      --tw-skew-x: initial;\\n      --tw-skew-y: initial;\\n      --tw-space-x-reverse: 0;\\n      --tw-border-style: solid;\\n      --tw-leading: initial;\\n      --tw-font-weight: initial;\\n      --tw-tracking: initial;\\n      --tw-ordinal: initial;\\n      --tw-slashed-zero: initial;\\n      --tw-numeric-figure: initial;\\n      --tw-numeric-spacing: initial;\\n      --tw-numeric-fraction: initial;\\n      --tw-shadow: 0 0 #0000;\\n      --tw-shadow-color: initial;\\n      --tw-shadow-alpha: 100%;\\n      --tw-inset-shadow: 0 0 #0000;\\n      --tw-inset-shadow-color: initial;\\n      --tw-inset-shadow-alpha: 100%;\\n      --tw-ring-color: initial;\\n      --tw-ring-shadow: 0 0 #0000;\\n      --tw-inset-ring-color: initial;\\n      --tw-inset-ring-shadow: 0 0 #0000;\\n      --tw-ring-inset: initial;\\n      --tw-ring-offset-width: 0px;\\n      --tw-ring-offset-color: #fff;\\n      --tw-ring-offset-shadow: 0 0 #0000;\\n      --tw-outline-style: solid;\\n      --tw-duration: initial;\\n      --tw-ease: initial;\\n      --tw-content: \\\"\\\";\\n      --tw-animation-delay: 0s;\\n      --tw-animation-direction: normal;\\n      --tw-animation-duration: initial;\\n      --tw-animation-fill-mode: none;\\n      --tw-animation-iteration-count: 1;\\n      --tw-enter-opacity: 1;\\n      --tw-enter-rotate: 0;\\n      --tw-enter-scale: 1;\\n      --tw-enter-translate-x: 0;\\n      --tw-enter-translate-y: 0;\\n      --tw-exit-opacity: 1;\\n      --tw-exit-rotate: 0;\\n      --tw-exit-scale: 1;\\n      --tw-exit-translate-x: 0;\\n      --tw-exit-translate-y: 0;\\n    }\\n  }\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"<no source>\",\"webpack://node_modules/tailwindcss/index.css\",\"webpack://node_modules/tw-animate-css/dist/tw-animate.css\",\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA,kEAAA;ACs3BE,iBAAmB;AAt3BrB,yCAAyC;AAEzC;EACE;IAqCE,4CAA4C;IAqC5C,6CAA6C;IAE7C,6CAA6C;IAsE7C,8CAA8C;IAqE9C,6CAA6C;IAC7C,6CAA6C;IAC7C,4CAA4C;IAG5C,6CAA6C;IAC7C,6CAA6C;IAO7C,2CAA2C;IAC3C,4CAA4C;IAG5C,4CAA4C;IAE5C,0CAA0C;IAuC1C,mBAAmB;IACnB,mBAAmB;IAEnB,kBAAkB;IAWlB,qBAAqB;IACrB,qBAAqB;IAUrB,kBAAkB;IAClB,sCAAsC;IACtC,mBAAmB;IACnB,0CAA0C;IAC1C,iBAAiB;IACjB,uCAAuC;IACvC,mBAAmB;IACnB,0CAA0C;IAG1C,kBAAkB;IAClB,sCAAsC;IAmBtC,yBAAyB;IACzB,yBAAyB;IACzB,2BAA2B;IAE3B,4BAA4B;IAI5B,0BAA0B;IAI1B,wBAAwB;IAExB,qBAAqB;IAMrB,qBAAqB;IA6CrB,2CAA2C;IAI3C,+DAA+D;IAoD/D,oCAAoC;IACpC,kEAAkE;IAClE,6CAAoD;IASpD,kDAAyD;EA5c5C;AADJ;AAmeb;EAOE;IAKE,sBAAsB;IACtB,SAAS;IACT,UAAU;IACV,eAAe;EAJM;EAiBvB;IAEE,gBAAgB;IAChB,8BAA8B;IAC9B,WAAW;IACX,2JASC;IACD,mEAGC;IACD,uEAGC;IACD,wCAAwC;EAtBpC;EA+BN;IACE,SAAS;IACT,cAAc;IACd,qBAAqB;EAHpB;EAUH;IACE,yCAAyC;IACzC,iCAAiC;EAFf;EASpB;IAME,kBAAkB;IAClB,oBAAoB;EAFnB;EASH;IACE,cAAc;IACd,gCAAgC;IAChC,wBAAwB;EAHxB;EAUF;IAEE,mBAAmB;EADd;EAWP;IAIE,gJAUC;IACD,wEAGC;IACD,4EAGC;IACD,cAAc;EApBZ;EA2BJ;IACE,cAAc;EADV;EAQN;IAEE,cAAc;IACd,cAAc;IACd,kBAAkB;IAClB,wBAAwB;EAJtB;EAOJ;IACE,eAAe;EADb;EAIJ;IACE,WAAW;EADT;EAUJ;IACE,cAAc;IACd,qBAAqB;IACrB,yBAAyB;EAHrB;EAUN;IACE,aAAa;EADC;EAQhB;IACE,wBAAwB;EADjB;EAQT;IACE,kBAAkB;EADZ;EAQR;IAGE,gBAAgB;EADb;EAUL;IAQE,cAAc;IACd,sBAAsB;EAFjB;EASP;IAEE,eAAe;IACf,YAAY;EAFR;EAYN;IAME,aAAa;IACb,8BAA8B;IAC9B,gCAAgC;IAChC,uBAAuB;IACvB,cAAc;IACd,gBAAgB;IAChB,6BAA6B;IAC7B,UAAU;EARW;EAevB;IACE,mBAAmB;EAD0B;EAQ/C;IACE,0BAA0B;EAD0B;EAQtD;IACE,sBAAsB;EADD;EAQvB;IACE,UAAU;EADE;EASd;IAEE;MACE,mBAAyD;MAAzD;QAAA,yDAAyD;MAAA;IAD7C;EADiC;EAUjD;IACE,gBAAgB;EADT;EAQT;IACE,wBAAwB;EADE;EAS5B;IACE,eAAe;IACf,mBAAmB;EAFS;EAS9B;IACE,oBAAoB;EADE;EAQxB;IACE,UAAU;EAD2B;EAIvC;IASE,gBAAgB;EADqB;EAQvC;IACE,gBAAgB;EADD;EAQjB;IAGE,kBAAkB;EADG;EAQvB;IAEE,YAAY;EADc;EAQ5B;IACE,wBAAwB;EADmB;AA3YnC;AAgZZ;EACE;IAAA,2BAAmB;IAAnB,2BAAmB;EAAA;EAAnB;IAAA,oBAAmB;EAAA;EAAnB;IAAA,kBAAmB;IAAnB,UAAmB;IAAnB,WAAmB;IAAnB,UAAmB;IAAnB,YAAmB;IAAnB,gBAAmB;IAAnB,sBAAmB;IAAnB,mBAAmB;IAAnB,eAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,eAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,qBAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,kCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,oBAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,iBAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,yCAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,iBAAmB;EAAA;EAAnB;IAAA,cAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,oBAAmB;EAAA;EAAnB;IAAA,cAAmB;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,+BAAmB;IAAnB,gCAAmB;EAAA;EAAnB;IAAA,iCAAmB;IAAnB,kCAAmB;EAAA;EAAnB;IAAA,iCAAmB;IAAnB,kCAAmB;EAAA;EAAnB;IAAA,+BAAmB;IAAnB,gCAAmB;EAAA;EAAnB;IAAA,+BAAmB;IAAnB,gCAAmB;EAAA;EAAnB;IAAA,+BAAmB;IAAnB,gCAAmB;EAAA;EAAnB;IAAA,+BAAmB;IAAnB,gCAAmB;EAAA;EAAnB;IAAA,+BAAmB;IAAnB,gCAAmB;EAAA;EAAnB;IAAA,WAAmB;IAAnB,YAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,wBAAmB;EAAA;EAAnB;IAAA,0CAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,cAAmB;EAAA;EAAnB;IAAA,+DAAmB;EAAA;EAAnB;IAAA,wDAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,2BAAmB;EAAA;EAAnB;IAAA,uBAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,0CAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,eAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,eAAmB;EAAA;EAAnB;IAAA,4CAAmB;EAAA;EAAnB;IAAA,OAAmB;EAAA;EAAnB;IAAA,cAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,oBAAmB;EAAA;EAAnB;IAAA,qEAAmB;EAAA;EAAnB;IAAA,kEAAmB;EAAA;EAAnB;IAAA,+DAAmB;EAAA;EAAnB;IAAA,+DAAmB;EAAA;EAAnB;IAAA,8DAAmB;EAAA;EAAnB;IAAA,+DAAmB;EAAA;EAAnB;IAAA,6CAAmB;IAAnB,sDAAmB;EAAA;EAAnB;IAAA,sBAAmB;IAAnB,sDAAmB;EAAA;EAAnB;IAAA,qBAAmB;IAAnB,sDAAmB;EAAA;EAAnB;IAAA,6CAAmB;IAAnB,sDAAmB;EAAA;EAAnB;IAAA,4CAAmB;IAAnB,sDAAmB;EAAA;EAAnB;IAAA,kCAAmB;IAAnB,sDAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,0GAAmB;EAAA;EAAnB;IAAA,+NAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,eAAmB;EAAA;EAAnB;IAAA,6CAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,2BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,eAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,uBAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,uBAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA;MAAA,uBAAmB;MAAnB,+EAAmB;MAAnB,uFAAmB;IAAA;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,gBAAmB;IAAnB,uBAAmB;IAAnB,mBAAmB;EAAA;EAAnB;IAAA,cAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,4CAAmB;IAAnB,+CAAmB;EAAA;EAAnB;IAAA,oCAAmB;IAAnB,iBAAmB;EAAA;EAAnB;IAAA,wCAAmB;IAAnB,qBAAmB;EAAA;EAAnB;IAAA,0CAAmB;IAAnB,uBAAmB;EAAA;EAAnB;IAAA,2CAAmB;IAAnB,wBAAmB;EAAA;EAAnB;IAAA,yCAAmB;IAAnB,sBAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,0BAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,2DAAmB;IAAnB;MAAA,0EAAmB;IAAA;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,8BAAmB;IAAnB;MAAA,oEAAmB;IAAA;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;IAAnB;MAAA,sEAAmB;IAAA;EAAA;EAAnB;IAAA,kCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,oBAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,0CAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,yCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,yCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,0BAAmB;IAAnB,4DAAmB;EAAA;EAAnB;IAAA,2BAAmB;IAAnB,6DAAmB;EAAA;EAAnB;IAAA,yBAAmB;IAAnB,2DAAmB;EAAA;EAAnB;IAAA,yBAAmB;IAAnB,2DAAmB;EAAA;EAAnB;IAAA,yBAAmB;IAAnB,2DAAmB;EAAA;EAAnB;IAAA,eAAmB;IAAnB,cAAmB;EAAA;EAAnB;IAAA,kCAAmB;IAAnB,iCAAmB;EAAA;EAAnB;IAAA,8CAAmB;IAAnB,yCAAmB;EAAA;EAAnB;IAAA,2CAAmB;IAAnB,sCAAmB;EAAA;EAAnB;IAAA,2CAAmB;IAAnB,sCAAmB;EAAA;EAAnB;IAAA,6CAAmB;IAAnB,wCAAmB;EAAA;EAAnB;IAAA,oCAAmB;IAAnB,qCAAmB;EAAA;EAAnB;IAAA,qCAAmB;IAAnB,sCAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,qBAAmB;EAAA;EAAnB;IAAA,wBAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,qBAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,kCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;IAAnB;MAAA,sEAAmB;IAAA;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,kCAAmB;IAAnB,iJAAmB;EAAA;EAAnB;IAAA,0BAAmB;EAAA;EAAnB;IAAA,mCAAmB;IAAnB,kCAAmB;EAAA;EAAnB;IAAA;MAAA,4BAAmB;IAAA;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,yEAAmB;IAAnB,sIAAmB;EAAA;EAAnB;IAAA,+HAAmB;IAAnB,sIAAmB;EAAA;EAAnB;IAAA,6HAAmB;IAAnB,sIAAmB;EAAA;EAAnB;IAAA,sBAAmB;IAAnB,sIAAmB;EAAA;EAAnB;IAAA,0HAAmB;IAAnB,sIAAmB;EAAA;EAAnB;IAAA,kEAAmB;IAAnB,sIAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,yCAAmB;EAAA;EAAnB;IAAA,wBAAmB;IAAnB,mBAAmB;IAAnB;MAAA,8BAAmB;MAAnB,mBAAmB;IAAA;EAAA;EAAnB;IAAA,sCAAmB;IAAnB,kBAAmB;EAAA;EAAnB;IAAA,qVAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,qCAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,qCAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,mCAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,yCAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,0BAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,wBAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,uKAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,4BAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,wDAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,oBAAmB;IAAnB,0BAAmB;EAAA;EAAnB;IAAA,oBAAmB;IAAnB,0BAAmB;EAAA;EAAnB;IAAA,6BAAmB;IAAnB,8CAAmB;EAAA;EAAnB;IAAA,iBAAmB;IAAnB,kCAAmB;EAAA;EAAnB;ICt3B6yL,+BAA6C;IAAE,qBAA+C;EDs3Bx3L;EAAnB;IAAA,wBAAmB;IAAnB,mBAAmB;EAAA;EAAnB;IAAA,yBAAmB;IAAnB,iBAAmB;EAAA;EAAnB;ICt3BgmM,6BAA0C;IAA0C,qBAA6C;EDs3B9sM;EAAnB;IAAA;MAAA,aAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,aAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,qCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,aAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0CAAmB;MAAnB,2CAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,mEAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yEAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,4CAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,4CAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,WAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,sCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,qCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0CAAmB;MAAnB,sDAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0CAAmB;MAAnB,uBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,cAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yCAAmB;MAAnB,sBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,aAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,4BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oCAAmB;MAAnB,iBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,mCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0HAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,uCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,6BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;IAAnB;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;IAAnB;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oCAAmB;MAAnB,iBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,6BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yBAAmB;MAAnB,2DAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,2CAAmB;MAAnB,sCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,6BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0BAAmB;MAAnB,kBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0BAAmB;MAAnB,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0BAAmB;MAAnB,qCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0BAAmB;MAAnB,sBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0BAAmB;MAAnB,UAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,0BAAmB;QAAnB,UAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,2CAAmB;MAAnB,wBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,oCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,+BAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,oCAAmB;QAAnB;UAAA,0EAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,wCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,8BAAmB;QAAnB;UAAA,oEAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,gCAAmB;QAAnB;UAAA,sEAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,kCAAmB;QAAnB;UAAA,wEAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,uCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,2DAAmB;QAAnB;UAAA,0EAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,+BAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,wBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,uCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,+BAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,aAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,0HAAmB;QAAnB,sIAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,yEAAmB;QAAnB,sIAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,gCAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,0BAAmB;UAAnB,uCAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,4BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0HAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,sBAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wHAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,4BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,2BAAmB;MAAnB,4GAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wBAAmB;MAAnB,mBAAmB;MAAnB;QAAA,8BAAmB;QAAnB,mBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,wBAAmB;MAAnB,mBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wHAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wHAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,mCAAmB;MAAnB;QAAA,yEAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,4BAAmB;MAAnB;QAAA,kEAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,sCAAmB;MAAnB,kBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,2DAAmB;MAAnB;QAAA,0EAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,sBAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,mBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,YAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,sBAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0CAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,YAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,mCAAmB;MAAnB;QAAA,yEAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,2CAAmB;MAAnB,sCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,YAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,sCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,WAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,WAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,YAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,UAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0CAAmB;MAAnB,sDAAmB;IAAA;EAAA;EAAnB;IAAA;MCt3BklP,iDAAgE;IDs3B/nP;EAAA;EAAnB;IAAA;MAAA,2CAAmB;MAAnB,sDAAmB;IAAA;EAAA;EAAnB;IAAA;MCt3BoqR,8CAA6D;IDs3B9sR;EAAA;EAAnB;IAAA;MAAA,0CAAmB;MAAnB,sDAAmB;IAAA;EAAA;EAAnB;IAAA;MCt3B+yQ,iDAAgE;IDs3B51Q;EAAA;EAAnB;IAAA;MAAA,2CAAmB;MAAnB,sDAAmB;IAAA;EAAA;EAAnB;IAAA;MCt3By8P,8CAA6D;IDs3Bn/P;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,gBAAmB;QAAnB,oBAAmB;QAAnB,4BAAmB;QAAnB,qBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,aAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,mBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,6BAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,8CAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0HAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,mFAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8NAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;MAAnB,0BAAmB;IAAA;EAAA;EAAnB;IAAA;MCt3By8L,8BAA4C;MAAE,oBAA8C;IDs3BlhM;EAAA;EAAnB;IAAA;MCt3Bw7M,4BAAyC;MAAyC,oBAA4C;IDs3BniN;EAAA;EAAnB;IAAA;MCt3Bu2V,2BAA2B;IDs3B/2V;EAAA;EAAnB;IAAA;MCt3BusW,4BAA4B;IDs3BhtW;EAAA;EAAnB;IAAA;MCt3BqjX,2BAA2B;IDs3B7jX;EAAA;EAAnB;IAAA;MCt3By/U,4BAA4B;IDs3BlgV;EAAA;EAAnB;IAAA;MAAA,qFAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+NAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,kCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,aAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;MAAnB,0BAAmB;IAAA;EAAA;EAAnB;IAAA;MCt3B6yL,+BAA6C;MAAE,qBAA+C;IDs3Bx3L;EAAA;EAAnB;IAAA;MCt3BgmM,6BAA0C;MAA0C,qBAA6C;IDs3B9sM;EAAA;EAAnB;IAAA;MCt3B24P,4BAA4B;IDs3Bp5P;EAAA;EAAnB;IAAA;MCt3BkvQ,6BAA6B;IDs3B5vQ;EAAA;EAAnB;IAAA;MCt3BumR,4BAA4B;IDs3BhnR;EAAA;EAAnB;IAAA;MCt3BshP,6BAA6B;IDs3BhiP;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,uCAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,uCAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,oCAAmB;QAAnB;UAAA,0EAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,yBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,mBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,cAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,aAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,WAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yBAAmB;MAAnB,2DAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,cAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,aAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yBAAmB;MAAnB,2DAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,WAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,gCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,qCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,wCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,0HAAmB;QAAnB,sIAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,qCAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,0BAAmB;QAAnB,aAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oCAAmB;MAAnB;QAAA,0EAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;MAAnB;QAAA,oEAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,+BAAmB;UAAnB;YAAA,qEAAmB;UAAA;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,8BAAmB;UAAnB;YAAA,oEAAmB;UAAA;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,mCAAmB;QAAnB;UAAA,yEAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,mCAAmB;QAAnB;UAAA,yEAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,0BAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,8BAAmB;QAAnB;UAAA,oEAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,wBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,oCAAmB;UAAnB;YAAA,0EAAmB;UAAA;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,cAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;MAAnB,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,2CAAmB;MAAnB,wBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oCAAmB;MAAnB,iBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,qCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,aAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,mBAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,6BAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,oCAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,qBAAmB;MAAnB,sDAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,aAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gBAAmB;MAAnB,uBAAmB;MAAnB,mBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;MAAnB,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,iCAAmB;MAAnB,kCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;MAAnB,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,cAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,2CAAmB;QAAnB,wBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,cAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,+BAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,oCAAmB;UAAnB;YAAA,0EAAmB;UAAA;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,gCAAmB;UAAnB;YAAA,sEAAmB;UAAA;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,kCAAmB;UAAnB;YAAA,wEAAmB;UAAA;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,+BAAmB;QAAA;MAAA;IAAA;EAAA;AADJ;ACr3BjB;EAA+B,WAAU;EAAC,eAAc;EAAC,iBAAgB;AAA3C;AAA4C;EAAmC,WAAU;EAAC,eAAc;EAAC,qBAAoB;AAA/C;AAAgD;EAAkC,WAAU;EAAC,eAAc;AAA1B;AAA2B;EAAmC,WAAU;EAAC,eAAc;EAAC,mBAAkB;AAA7C;AAA8C;EAAyC,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAA6B,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAA4B,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAA2B,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAAiC,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAAiC,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAA4B,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAA2B,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAA0B,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAAgC,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAAgC,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;ACK3hC;EACE,qBAAsB;EACtB,qBAAsB;EACtB,oBAAqB;EACrB,mCAAoC;EACpC,uBAAwB;EACxB,sCAAuC;EAGvC,sCAAuC;EACvC,sCAAuC;EAEvC,wCAAyC;EAEzC,wCAAyC;EACzC,wBAAyB;EACzB,oCAAqC;EACrC,yBAA0B;EAC1B,qCAAsC;EACtC,wCAAyC;EACzC,mDAAoD;EACpD,0BAA2B;EAC3B,yBAA0B;EAC1B,wBAAyB;EACzB,oCAAqC;EACrC,mCAAoC;EACpC,oCAAqC;EACrC,oCAAqC;EACrC,mCAAoC;EACpC,kBAAmB;EACnB,2BAA4B;EAC5B,sCAAuC;EACvC,mCAAoC;EACpC,8CAA+C;EAC/C,iCAAkC;EAClC,6CAA8C;EAC9C,kCAAmC;EACnC,gCAAiC;EAEjC,mCAAoC;EACpC,oCAAqC;EACrC,wCAAyC;EACzC,sCAAuC;EACvC,sCAAuC;EACvC,wCAAyC;EACzC,wCAAyC;EACzC,qCAAsC;EACtC,oCAAqC;EACrC,mCAAoC;EACpC,qCAAsC;EACtC,yCAA0C;AAC3C;AAgED;EACE,6BAA8B;EAC9B,wBAAyB;EACzB,yCAA0C;AAC3C;AAED;EACE,8BAA+B;EAC/B,8BAA+B;EAC/B,wBAAyB;EACzB,mCAAoC;EACpC,2BAA4B;EAC5B,sCAAuC;EACvC,2BAA4B;EAC5B,sCAAuC;EACvC,6BAA8B;EAC9B,wCAAyC;EACzC,yBAA0B;EAC1B,oCAAqC;EACrC,0BAA2B;EAC3B,qCAAsC;EACtC,wCAAyC;EACzC,mDAAoD;EACpD,0BAA2B;EAC3B,yBAA0B;EAC1B,wBAAyB;EACzB,qCAAsC;EACtC,mCAAoC;EACpC,mCAAoC;EACpC,mCAAoC;EACpC,oCAAqC;EACrC,2BAA4B;EAC5B,sCAAuC;EACvC,6CAA8C;EAC9C,8CAA+C;EAC/C,kCAAmC;EACnC,6CAA8C;EAC9C,kCAAmC;EACnC,gCAAiC;AAClC;AAED;EACE;IACS,2BAAa;IAAC,0BAAe;IAAf;MAAA,gEAAe;IAAA;EACrC;EACD;IACS,mCAAa;IAAC,wBAAe;EACrC;AACF;AAED;EACE;IACE,8BAA+B;IAC/B,oCAAqC;IACrC,+BAAgC;IAChC,sCAAuC;IACvC,gCAAiC;IACjC,yCAA0C;IAC1C,6BAA8B;IAC9B,iCAAkC;EACnC;EAED;IACE,kCAAmC;IACnC,oCAAqC;IACrC,kCAAmC;IACnC,uCAAwC;IACxC,gCAAiC;IACjC,2CAA4C;IAC5C,gCAAiC;IACjC,iCAAkC;EACnC;AACF;AFsrBC;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,gBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,gBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,gBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,gBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,oBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,sBAAmB;EAAnB,eAAmB;EAAnB,mBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,sBAAmB;EAAnB,eAAmB;EAAnB,mBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,kBAAmB;EAAnB,eAAmB;EAAnB,kBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,mBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,oBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,iBAAmB;EAAnB,eAAmB;AAAA;AArdjB;EACE;IACE,YAAY;EADV;AADW;ACja0oF;EAAmB;IAAO,kCAAkC;IAAE,qMAAqM;EAA3O;AAAP;AAAsP;EAAkB;IAAK,iCAAiC;IAAE,+LAA+L;EAApO;AAAL;AAAqoB;EAA4B;IAAO,SAAS;EAAX;EAAc;IAAK,+JAA+J;EAAjK;AAAxB;AAA6L;EAA0B;IAAO,+JAA+J;EAAjK;EAAoK;IAAK,SAAS;EAAX;AAA9K;ADs3BxyH;EAAA;IAAA;MAAA,mBAAmB;MAAnB,mBAAmB;MAAnB,mBAAmB;MAAnB,sBAAmB;MAAnB,sBAAmB;MAAnB,sBAAmB;MAAnB,oBAAmB;MAAnB,oBAAmB;MAAnB,uBAAmB;MAAnB,wBAAmB;MAAnB,qBAAmB;MAAnB,yBAAmB;MAAnB,sBAAmB;MAAnB,qBAAmB;MAAnB,0BAAmB;MAAnB,4BAAmB;MAAnB,6BAAmB;MAAnB,8BAAmB;MAAnB,sBAAmB;MAAnB,0BAAmB;MAAnB,uBAAmB;MAAnB,4BAAmB;MAAnB,gCAAmB;MAAnB,6BAAmB;MAAnB,wBAAmB;MAAnB,2BAAmB;MAAnB,8BAAmB;MAAnB,iCAAmB;MAAnB,wBAAmB;MAAnB,2BAAmB;MAAnB,4BAAmB;MAAnB,kCAAmB;MAAnB,yBAAmB;MAAnB,sBAAmB;MAAnB,kBAAmB;MAAnB,gBAAmB;MCt3BrB,wBAA8B;MAA4C,gCAAkC;MAAgD,gCAAiC;MAA2B,8BAAkC;MAA8C,iCAAwC;MAA2C,qBAA4B;MAA2C,oBAA2B;MAA2C,mBAA0B;MAA2C,yBAAgC;MAA2C,yBAAgC;MAA2C,oBAA2B;MAA2C,mBAA0B;MAA2C,kBAAyB;MAA2C,wBAA+B;MAA2C,wBAA+B;IDs3BtgC;EAAA;AAAA\",\"sourcesContent\":[null,\"@layer theme, base, components, utilities;\\n\\n@layer theme {\\n  @theme default {\\n    --font-sans:\\n      ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\",\\n      \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\";\\n    --font-serif: ui-serif, Georgia, Cambria, \\\"Times New Roman\\\", Times, serif;\\n    --font-mono:\\n      ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\",\\n      \\\"Courier New\\\", monospace;\\n\\n    --color-red-50: oklch(97.1% 0.013 17.38);\\n    --color-red-100: oklch(93.6% 0.032 17.717);\\n    --color-red-200: oklch(88.5% 0.062 18.334);\\n    --color-red-300: oklch(80.8% 0.114 19.571);\\n    --color-red-400: oklch(70.4% 0.191 22.216);\\n    --color-red-500: oklch(63.7% 0.237 25.331);\\n    --color-red-600: oklch(57.7% 0.245 27.325);\\n    --color-red-700: oklch(50.5% 0.213 27.518);\\n    --color-red-800: oklch(44.4% 0.177 26.899);\\n    --color-red-900: oklch(39.6% 0.141 25.723);\\n    --color-red-950: oklch(25.8% 0.092 26.042);\\n\\n    --color-orange-50: oklch(98% 0.016 73.684);\\n    --color-orange-100: oklch(95.4% 0.038 75.164);\\n    --color-orange-200: oklch(90.1% 0.076 70.697);\\n    --color-orange-300: oklch(83.7% 0.128 66.29);\\n    --color-orange-400: oklch(75% 0.183 55.934);\\n    --color-orange-500: oklch(70.5% 0.213 47.604);\\n    --color-orange-600: oklch(64.6% 0.222 41.116);\\n    --color-orange-700: oklch(55.3% 0.195 38.402);\\n    --color-orange-800: oklch(47% 0.157 37.304);\\n    --color-orange-900: oklch(40.8% 0.123 38.172);\\n    --color-orange-950: oklch(26.6% 0.079 36.259);\\n\\n    --color-amber-50: oklch(98.7% 0.022 95.277);\\n    --color-amber-100: oklch(96.2% 0.059 95.617);\\n    --color-amber-200: oklch(92.4% 0.12 95.746);\\n    --color-amber-300: oklch(87.9% 0.169 91.605);\\n    --color-amber-400: oklch(82.8% 0.189 84.429);\\n    --color-amber-500: oklch(76.9% 0.188 70.08);\\n    --color-amber-600: oklch(66.6% 0.179 58.318);\\n    --color-amber-700: oklch(55.5% 0.163 48.998);\\n    --color-amber-800: oklch(47.3% 0.137 46.201);\\n    --color-amber-900: oklch(41.4% 0.112 45.904);\\n    --color-amber-950: oklch(27.9% 0.077 45.635);\\n\\n    --color-yellow-50: oklch(98.7% 0.026 102.212);\\n    --color-yellow-100: oklch(97.3% 0.071 103.193);\\n    --color-yellow-200: oklch(94.5% 0.129 101.54);\\n    --color-yellow-300: oklch(90.5% 0.182 98.111);\\n    --color-yellow-400: oklch(85.2% 0.199 91.936);\\n    --color-yellow-500: oklch(79.5% 0.184 86.047);\\n    --color-yellow-600: oklch(68.1% 0.162 75.834);\\n    --color-yellow-700: oklch(55.4% 0.135 66.442);\\n    --color-yellow-800: oklch(47.6% 0.114 61.907);\\n    --color-yellow-900: oklch(42.1% 0.095 57.708);\\n    --color-yellow-950: oklch(28.6% 0.066 53.813);\\n\\n    --color-lime-50: oklch(98.6% 0.031 120.757);\\n    --color-lime-100: oklch(96.7% 0.067 122.328);\\n    --color-lime-200: oklch(93.8% 0.127 124.321);\\n    --color-lime-300: oklch(89.7% 0.196 126.665);\\n    --color-lime-400: oklch(84.1% 0.238 128.85);\\n    --color-lime-500: oklch(76.8% 0.233 130.85);\\n    --color-lime-600: oklch(64.8% 0.2 131.684);\\n    --color-lime-700: oklch(53.2% 0.157 131.589);\\n    --color-lime-800: oklch(45.3% 0.124 130.933);\\n    --color-lime-900: oklch(40.5% 0.101 131.063);\\n    --color-lime-950: oklch(27.4% 0.072 132.109);\\n\\n    --color-green-50: oklch(98.2% 0.018 155.826);\\n    --color-green-100: oklch(96.2% 0.044 156.743);\\n    --color-green-200: oklch(92.5% 0.084 155.995);\\n    --color-green-300: oklch(87.1% 0.15 154.449);\\n    --color-green-400: oklch(79.2% 0.209 151.711);\\n    --color-green-500: oklch(72.3% 0.219 149.579);\\n    --color-green-600: oklch(62.7% 0.194 149.214);\\n    --color-green-700: oklch(52.7% 0.154 150.069);\\n    --color-green-800: oklch(44.8% 0.119 151.328);\\n    --color-green-900: oklch(39.3% 0.095 152.535);\\n    --color-green-950: oklch(26.6% 0.065 152.934);\\n\\n    --color-emerald-50: oklch(97.9% 0.021 166.113);\\n    --color-emerald-100: oklch(95% 0.052 163.051);\\n    --color-emerald-200: oklch(90.5% 0.093 164.15);\\n    --color-emerald-300: oklch(84.5% 0.143 164.978);\\n    --color-emerald-400: oklch(76.5% 0.177 163.223);\\n    --color-emerald-500: oklch(69.6% 0.17 162.48);\\n    --color-emerald-600: oklch(59.6% 0.145 163.225);\\n    --color-emerald-700: oklch(50.8% 0.118 165.612);\\n    --color-emerald-800: oklch(43.2% 0.095 166.913);\\n    --color-emerald-900: oklch(37.8% 0.077 168.94);\\n    --color-emerald-950: oklch(26.2% 0.051 172.552);\\n\\n    --color-teal-50: oklch(98.4% 0.014 180.72);\\n    --color-teal-100: oklch(95.3% 0.051 180.801);\\n    --color-teal-200: oklch(91% 0.096 180.426);\\n    --color-teal-300: oklch(85.5% 0.138 181.071);\\n    --color-teal-400: oklch(77.7% 0.152 181.912);\\n    --color-teal-500: oklch(70.4% 0.14 182.503);\\n    --color-teal-600: oklch(60% 0.118 184.704);\\n    --color-teal-700: oklch(51.1% 0.096 186.391);\\n    --color-teal-800: oklch(43.7% 0.078 188.216);\\n    --color-teal-900: oklch(38.6% 0.063 188.416);\\n    --color-teal-950: oklch(27.7% 0.046 192.524);\\n\\n    --color-cyan-50: oklch(98.4% 0.019 200.873);\\n    --color-cyan-100: oklch(95.6% 0.045 203.388);\\n    --color-cyan-200: oklch(91.7% 0.08 205.041);\\n    --color-cyan-300: oklch(86.5% 0.127 207.078);\\n    --color-cyan-400: oklch(78.9% 0.154 211.53);\\n    --color-cyan-500: oklch(71.5% 0.143 215.221);\\n    --color-cyan-600: oklch(60.9% 0.126 221.723);\\n    --color-cyan-700: oklch(52% 0.105 223.128);\\n    --color-cyan-800: oklch(45% 0.085 224.283);\\n    --color-cyan-900: oklch(39.8% 0.07 227.392);\\n    --color-cyan-950: oklch(30.2% 0.056 229.695);\\n\\n    --color-sky-50: oklch(97.7% 0.013 236.62);\\n    --color-sky-100: oklch(95.1% 0.026 236.824);\\n    --color-sky-200: oklch(90.1% 0.058 230.902);\\n    --color-sky-300: oklch(82.8% 0.111 230.318);\\n    --color-sky-400: oklch(74.6% 0.16 232.661);\\n    --color-sky-500: oklch(68.5% 0.169 237.323);\\n    --color-sky-600: oklch(58.8% 0.158 241.966);\\n    --color-sky-700: oklch(50% 0.134 242.749);\\n    --color-sky-800: oklch(44.3% 0.11 240.79);\\n    --color-sky-900: oklch(39.1% 0.09 240.876);\\n    --color-sky-950: oklch(29.3% 0.066 243.157);\\n\\n    --color-blue-50: oklch(97% 0.014 254.604);\\n    --color-blue-100: oklch(93.2% 0.032 255.585);\\n    --color-blue-200: oklch(88.2% 0.059 254.128);\\n    --color-blue-300: oklch(80.9% 0.105 251.813);\\n    --color-blue-400: oklch(70.7% 0.165 254.624);\\n    --color-blue-500: oklch(62.3% 0.214 259.815);\\n    --color-blue-600: oklch(54.6% 0.245 262.881);\\n    --color-blue-700: oklch(48.8% 0.243 264.376);\\n    --color-blue-800: oklch(42.4% 0.199 265.638);\\n    --color-blue-900: oklch(37.9% 0.146 265.522);\\n    --color-blue-950: oklch(28.2% 0.091 267.935);\\n\\n    --color-indigo-50: oklch(96.2% 0.018 272.314);\\n    --color-indigo-100: oklch(93% 0.034 272.788);\\n    --color-indigo-200: oklch(87% 0.065 274.039);\\n    --color-indigo-300: oklch(78.5% 0.115 274.713);\\n    --color-indigo-400: oklch(67.3% 0.182 276.935);\\n    --color-indigo-500: oklch(58.5% 0.233 277.117);\\n    --color-indigo-600: oklch(51.1% 0.262 276.966);\\n    --color-indigo-700: oklch(45.7% 0.24 277.023);\\n    --color-indigo-800: oklch(39.8% 0.195 277.366);\\n    --color-indigo-900: oklch(35.9% 0.144 278.697);\\n    --color-indigo-950: oklch(25.7% 0.09 281.288);\\n\\n    --color-violet-50: oklch(96.9% 0.016 293.756);\\n    --color-violet-100: oklch(94.3% 0.029 294.588);\\n    --color-violet-200: oklch(89.4% 0.057 293.283);\\n    --color-violet-300: oklch(81.1% 0.111 293.571);\\n    --color-violet-400: oklch(70.2% 0.183 293.541);\\n    --color-violet-500: oklch(60.6% 0.25 292.717);\\n    --color-violet-600: oklch(54.1% 0.281 293.009);\\n    --color-violet-700: oklch(49.1% 0.27 292.581);\\n    --color-violet-800: oklch(43.2% 0.232 292.759);\\n    --color-violet-900: oklch(38% 0.189 293.745);\\n    --color-violet-950: oklch(28.3% 0.141 291.089);\\n\\n    --color-purple-50: oklch(97.7% 0.014 308.299);\\n    --color-purple-100: oklch(94.6% 0.033 307.174);\\n    --color-purple-200: oklch(90.2% 0.063 306.703);\\n    --color-purple-300: oklch(82.7% 0.119 306.383);\\n    --color-purple-400: oklch(71.4% 0.203 305.504);\\n    --color-purple-500: oklch(62.7% 0.265 303.9);\\n    --color-purple-600: oklch(55.8% 0.288 302.321);\\n    --color-purple-700: oklch(49.6% 0.265 301.924);\\n    --color-purple-800: oklch(43.8% 0.218 303.724);\\n    --color-purple-900: oklch(38.1% 0.176 304.987);\\n    --color-purple-950: oklch(29.1% 0.149 302.717);\\n\\n    --color-fuchsia-50: oklch(97.7% 0.017 320.058);\\n    --color-fuchsia-100: oklch(95.2% 0.037 318.852);\\n    --color-fuchsia-200: oklch(90.3% 0.076 319.62);\\n    --color-fuchsia-300: oklch(83.3% 0.145 321.434);\\n    --color-fuchsia-400: oklch(74% 0.238 322.16);\\n    --color-fuchsia-500: oklch(66.7% 0.295 322.15);\\n    --color-fuchsia-600: oklch(59.1% 0.293 322.896);\\n    --color-fuchsia-700: oklch(51.8% 0.253 323.949);\\n    --color-fuchsia-800: oklch(45.2% 0.211 324.591);\\n    --color-fuchsia-900: oklch(40.1% 0.17 325.612);\\n    --color-fuchsia-950: oklch(29.3% 0.136 325.661);\\n\\n    --color-pink-50: oklch(97.1% 0.014 343.198);\\n    --color-pink-100: oklch(94.8% 0.028 342.258);\\n    --color-pink-200: oklch(89.9% 0.061 343.231);\\n    --color-pink-300: oklch(82.3% 0.12 346.018);\\n    --color-pink-400: oklch(71.8% 0.202 349.761);\\n    --color-pink-500: oklch(65.6% 0.241 354.308);\\n    --color-pink-600: oklch(59.2% 0.249 0.584);\\n    --color-pink-700: oklch(52.5% 0.223 3.958);\\n    --color-pink-800: oklch(45.9% 0.187 3.815);\\n    --color-pink-900: oklch(40.8% 0.153 2.432);\\n    --color-pink-950: oklch(28.4% 0.109 3.907);\\n\\n    --color-rose-50: oklch(96.9% 0.015 12.422);\\n    --color-rose-100: oklch(94.1% 0.03 12.58);\\n    --color-rose-200: oklch(89.2% 0.058 10.001);\\n    --color-rose-300: oklch(81% 0.117 11.638);\\n    --color-rose-400: oklch(71.2% 0.194 13.428);\\n    --color-rose-500: oklch(64.5% 0.246 16.439);\\n    --color-rose-600: oklch(58.6% 0.253 17.585);\\n    --color-rose-700: oklch(51.4% 0.222 16.935);\\n    --color-rose-800: oklch(45.5% 0.188 13.697);\\n    --color-rose-900: oklch(41% 0.159 10.272);\\n    --color-rose-950: oklch(27.1% 0.105 12.094);\\n\\n    --color-slate-50: oklch(98.4% 0.003 247.858);\\n    --color-slate-100: oklch(96.8% 0.007 247.896);\\n    --color-slate-200: oklch(92.9% 0.013 255.508);\\n    --color-slate-300: oklch(86.9% 0.022 252.894);\\n    --color-slate-400: oklch(70.4% 0.04 256.788);\\n    --color-slate-500: oklch(55.4% 0.046 257.417);\\n    --color-slate-600: oklch(44.6% 0.043 257.281);\\n    --color-slate-700: oklch(37.2% 0.044 257.287);\\n    --color-slate-800: oklch(27.9% 0.041 260.031);\\n    --color-slate-900: oklch(20.8% 0.042 265.755);\\n    --color-slate-950: oklch(12.9% 0.042 264.695);\\n\\n    --color-gray-50: oklch(98.5% 0.002 247.839);\\n    --color-gray-100: oklch(96.7% 0.003 264.542);\\n    --color-gray-200: oklch(92.8% 0.006 264.531);\\n    --color-gray-300: oklch(87.2% 0.01 258.338);\\n    --color-gray-400: oklch(70.7% 0.022 261.325);\\n    --color-gray-500: oklch(55.1% 0.027 264.364);\\n    --color-gray-600: oklch(44.6% 0.03 256.802);\\n    --color-gray-700: oklch(37.3% 0.034 259.733);\\n    --color-gray-800: oklch(27.8% 0.033 256.848);\\n    --color-gray-900: oklch(21% 0.034 264.665);\\n    --color-gray-950: oklch(13% 0.028 261.692);\\n\\n    --color-zinc-50: oklch(98.5% 0 0);\\n    --color-zinc-100: oklch(96.7% 0.001 286.375);\\n    --color-zinc-200: oklch(92% 0.004 286.32);\\n    --color-zinc-300: oklch(87.1% 0.006 286.286);\\n    --color-zinc-400: oklch(70.5% 0.015 286.067);\\n    --color-zinc-500: oklch(55.2% 0.016 285.938);\\n    --color-zinc-600: oklch(44.2% 0.017 285.786);\\n    --color-zinc-700: oklch(37% 0.013 285.805);\\n    --color-zinc-800: oklch(27.4% 0.006 286.033);\\n    --color-zinc-900: oklch(21% 0.006 285.885);\\n    --color-zinc-950: oklch(14.1% 0.005 285.823);\\n\\n    --color-neutral-50: oklch(98.5% 0 0);\\n    --color-neutral-100: oklch(97% 0 0);\\n    --color-neutral-200: oklch(92.2% 0 0);\\n    --color-neutral-300: oklch(87% 0 0);\\n    --color-neutral-400: oklch(70.8% 0 0);\\n    --color-neutral-500: oklch(55.6% 0 0);\\n    --color-neutral-600: oklch(43.9% 0 0);\\n    --color-neutral-700: oklch(37.1% 0 0);\\n    --color-neutral-800: oklch(26.9% 0 0);\\n    --color-neutral-900: oklch(20.5% 0 0);\\n    --color-neutral-950: oklch(14.5% 0 0);\\n\\n    --color-stone-50: oklch(98.5% 0.001 106.423);\\n    --color-stone-100: oklch(97% 0.001 106.424);\\n    --color-stone-200: oklch(92.3% 0.003 48.717);\\n    --color-stone-300: oklch(86.9% 0.005 56.366);\\n    --color-stone-400: oklch(70.9% 0.01 56.259);\\n    --color-stone-500: oklch(55.3% 0.013 58.071);\\n    --color-stone-600: oklch(44.4% 0.011 73.639);\\n    --color-stone-700: oklch(37.4% 0.01 67.558);\\n    --color-stone-800: oklch(26.8% 0.007 34.298);\\n    --color-stone-900: oklch(21.6% 0.006 56.043);\\n    --color-stone-950: oklch(14.7% 0.004 49.25);\\n\\n    --color-black: #000;\\n    --color-white: #fff;\\n\\n    --spacing: 0.25rem;\\n\\n    --breakpoint-sm: 40rem;\\n    --breakpoint-md: 48rem;\\n    --breakpoint-lg: 64rem;\\n    --breakpoint-xl: 80rem;\\n    --breakpoint-2xl: 96rem;\\n\\n    --container-3xs: 16rem;\\n    --container-2xs: 18rem;\\n    --container-xs: 20rem;\\n    --container-sm: 24rem;\\n    --container-md: 28rem;\\n    --container-lg: 32rem;\\n    --container-xl: 36rem;\\n    --container-2xl: 42rem;\\n    --container-3xl: 48rem;\\n    --container-4xl: 56rem;\\n    --container-5xl: 64rem;\\n    --container-6xl: 72rem;\\n    --container-7xl: 80rem;\\n\\n    --text-xs: 0.75rem;\\n    --text-xs--line-height: calc(1 / 0.75);\\n    --text-sm: 0.875rem;\\n    --text-sm--line-height: calc(1.25 / 0.875);\\n    --text-base: 1rem;\\n    --text-base--line-height: calc(1.5 / 1);\\n    --text-lg: 1.125rem;\\n    --text-lg--line-height: calc(1.75 / 1.125);\\n    --text-xl: 1.25rem;\\n    --text-xl--line-height: calc(1.75 / 1.25);\\n    --text-2xl: 1.5rem;\\n    --text-2xl--line-height: calc(2 / 1.5);\\n    --text-3xl: 1.875rem;\\n    --text-3xl--line-height: calc(2.25 / 1.875);\\n    --text-4xl: 2.25rem;\\n    --text-4xl--line-height: calc(2.5 / 2.25);\\n    --text-5xl: 3rem;\\n    --text-5xl--line-height: 1;\\n    --text-6xl: 3.75rem;\\n    --text-6xl--line-height: 1;\\n    --text-7xl: 4.5rem;\\n    --text-7xl--line-height: 1;\\n    --text-8xl: 6rem;\\n    --text-8xl--line-height: 1;\\n    --text-9xl: 8rem;\\n    --text-9xl--line-height: 1;\\n\\n    --font-weight-thin: 100;\\n    --font-weight-extralight: 200;\\n    --font-weight-light: 300;\\n    --font-weight-normal: 400;\\n    --font-weight-medium: 500;\\n    --font-weight-semibold: 600;\\n    --font-weight-bold: 700;\\n    --font-weight-extrabold: 800;\\n    --font-weight-black: 900;\\n\\n    --tracking-tighter: -0.05em;\\n    --tracking-tight: -0.025em;\\n    --tracking-normal: 0em;\\n    --tracking-wide: 0.025em;\\n    --tracking-wider: 0.05em;\\n    --tracking-widest: 0.1em;\\n\\n    --leading-tight: 1.25;\\n    --leading-snug: 1.375;\\n    --leading-normal: 1.5;\\n    --leading-relaxed: 1.625;\\n    --leading-loose: 2;\\n\\n    --radius-xs: 0.125rem;\\n    --radius-sm: 0.25rem;\\n    --radius-md: 0.375rem;\\n    --radius-lg: 0.5rem;\\n    --radius-xl: 0.75rem;\\n    --radius-2xl: 1rem;\\n    --radius-3xl: 1.5rem;\\n    --radius-4xl: 2rem;\\n\\n    --shadow-2xs: 0 1px rgb(0 0 0 / 0.05);\\n    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n    --shadow-md:\\n      0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n    --shadow-lg:\\n      0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n    --shadow-xl:\\n      0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n\\n    --inset-shadow-2xs: inset 0 1px rgb(0 0 0 / 0.05);\\n    --inset-shadow-xs: inset 0 1px 1px rgb(0 0 0 / 0.05);\\n    --inset-shadow-sm: inset 0 2px 4px rgb(0 0 0 / 0.05);\\n\\n    --drop-shadow-xs: 0 1px 1px rgb(0 0 0 / 0.05);\\n    --drop-shadow-sm: 0 1px 2px rgb(0 0 0 / 0.15);\\n    --drop-shadow-md: 0 3px 3px rgb(0 0 0 / 0.12);\\n    --drop-shadow-lg: 0 4px 4px rgb(0 0 0 / 0.15);\\n    --drop-shadow-xl: 0 9px 7px rgb(0 0 0 / 0.1);\\n    --drop-shadow-2xl: 0 25px 25px rgb(0 0 0 / 0.15);\\n\\n    --text-shadow-2xs: 0px 1px 0px rgb(0 0 0 / 0.15);\\n    --text-shadow-xs: 0px 1px 1px rgb(0 0 0 / 0.2);\\n    --text-shadow-sm:\\n      0px 1px 0px rgb(0 0 0 / 0.075), 0px 1px 1px rgb(0 0 0 / 0.075),\\n      0px 2px 2px rgb(0 0 0 / 0.075);\\n    --text-shadow-md:\\n      0px 1px 1px rgb(0 0 0 / 0.1), 0px 1px 2px rgb(0 0 0 / 0.1),\\n      0px 2px 4px rgb(0 0 0 / 0.1);\\n    --text-shadow-lg:\\n      0px 1px 2px rgb(0 0 0 / 0.1), 0px 3px 2px rgb(0 0 0 / 0.1),\\n      0px 4px 8px rgb(0 0 0 / 0.1);\\n\\n    --ease-in: cubic-bezier(0.4, 0, 1, 1);\\n    --ease-out: cubic-bezier(0, 0, 0.2, 1);\\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\\n\\n    --animate-spin: spin 1s linear infinite;\\n    --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;\\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n    --animate-bounce: bounce 1s infinite;\\n\\n    @keyframes spin {\\n      to {\\n        transform: rotate(360deg);\\n      }\\n    }\\n\\n    @keyframes ping {\\n      75%,\\n      100% {\\n        transform: scale(2);\\n        opacity: 0;\\n      }\\n    }\\n\\n    @keyframes pulse {\\n      50% {\\n        opacity: 0.5;\\n      }\\n    }\\n\\n    @keyframes bounce {\\n      0%,\\n      100% {\\n        transform: translateY(-25%);\\n        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n      }\\n\\n      50% {\\n        transform: none;\\n        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n      }\\n    }\\n\\n    --blur-xs: 4px;\\n    --blur-sm: 8px;\\n    --blur-md: 12px;\\n    --blur-lg: 16px;\\n    --blur-xl: 24px;\\n    --blur-2xl: 40px;\\n    --blur-3xl: 64px;\\n\\n    --perspective-dramatic: 100px;\\n    --perspective-near: 300px;\\n    --perspective-normal: 500px;\\n    --perspective-midrange: 800px;\\n    --perspective-distant: 1200px;\\n\\n    --aspect-video: 16 / 9;\\n\\n    --default-transition-duration: 150ms;\\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n    --default-font-family: --theme(--font-sans, initial);\\n    --default-font-feature-settings: --theme(\\n      --font-sans--font-feature-settings,\\n      initial\\n    );\\n    --default-font-variation-settings: --theme(\\n      --font-sans--font-variation-settings,\\n      initial\\n    );\\n    --default-mono-font-family: --theme(--font-mono, initial);\\n    --default-mono-font-feature-settings: --theme(\\n      --font-mono--font-feature-settings,\\n      initial\\n    );\\n    --default-mono-font-variation-settings: --theme(\\n      --font-mono--font-variation-settings,\\n      initial\\n    );\\n  }\\n\\n  /* Deprecated */\\n  @theme default inline reference {\\n    --blur: 8px;\\n    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n    --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);\\n    --drop-shadow: 0 1px 2px rgb(0 0 0 / 0.1), 0 1px 1px rgb(0 0 0 / 0.06);\\n    --radius: 0.25rem;\\n    --max-width-prose: 65ch;\\n  }\\n}\\n\\n@layer base {\\n  /*\\n  1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n  2. Remove default margins and padding\\n  3. Reset all borders.\\n*/\\n\\n  *,\\n  ::after,\\n  ::before,\\n  ::backdrop,\\n  ::file-selector-button {\\n    box-sizing: border-box; /* 1 */\\n    margin: 0; /* 2 */\\n    padding: 0; /* 2 */\\n    border: 0 solid; /* 3 */\\n  }\\n\\n  /*\\n  1. Use a consistent sensible line-height in all browsers.\\n  2. Prevent adjustments of font size after orientation changes in iOS.\\n  3. Use a more readable tab size.\\n  4. Use the user's configured `sans` font-family by default.\\n  5. Use the user's configured `sans` font-feature-settings by default.\\n  6. Use the user's configured `sans` font-variation-settings by default.\\n  7. Disable tap highlights on iOS.\\n*/\\n\\n  html,\\n  :host {\\n    line-height: 1.5; /* 1 */\\n    -webkit-text-size-adjust: 100%; /* 2 */\\n    tab-size: 4; /* 3 */\\n    font-family: --theme(\\n      --default-font-family,\\n      ui-sans-serif,\\n      system-ui,\\n      sans-serif,\\n      \\\"Apple Color Emoji\\\",\\n      \\\"Segoe UI Emoji\\\",\\n      \\\"Segoe UI Symbol\\\",\\n      \\\"Noto Color Emoji\\\"\\n    ); /* 4 */\\n    font-feature-settings: --theme(\\n      --default-font-feature-settings,\\n      normal\\n    ); /* 5 */\\n    font-variation-settings: --theme(\\n      --default-font-variation-settings,\\n      normal\\n    ); /* 6 */\\n    -webkit-tap-highlight-color: transparent; /* 7 */\\n  }\\n\\n  /*\\n  1. Add the correct height in Firefox.\\n  2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n  3. Reset the default border style to a 1px solid border.\\n*/\\n\\n  hr {\\n    height: 0; /* 1 */\\n    color: inherit; /* 2 */\\n    border-top-width: 1px; /* 3 */\\n  }\\n\\n  /*\\n  Add the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\n  abbr:where([title]) {\\n    -webkit-text-decoration: underline dotted;\\n    text-decoration: underline dotted;\\n  }\\n\\n  /*\\n  Remove the default font size and weight for headings.\\n*/\\n\\n  h1,\\n  h2,\\n  h3,\\n  h4,\\n  h5,\\n  h6 {\\n    font-size: inherit;\\n    font-weight: inherit;\\n  }\\n\\n  /*\\n  Reset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\n  a {\\n    color: inherit;\\n    -webkit-text-decoration: inherit;\\n    text-decoration: inherit;\\n  }\\n\\n  /*\\n  Add the correct font weight in Edge and Safari.\\n*/\\n\\n  b,\\n  strong {\\n    font-weight: bolder;\\n  }\\n\\n  /*\\n  1. Use the user's configured `mono` font-family by default.\\n  2. Use the user's configured `mono` font-feature-settings by default.\\n  3. Use the user's configured `mono` font-variation-settings by default.\\n  4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\n  code,\\n  kbd,\\n  samp,\\n  pre {\\n    font-family: --theme(\\n      --default-mono-font-family,\\n      ui-monospace,\\n      SFMono-Regular,\\n      Menlo,\\n      Monaco,\\n      Consolas,\\n      \\\"Liberation Mono\\\",\\n      \\\"Courier New\\\",\\n      monospace\\n    ); /* 1 */\\n    font-feature-settings: --theme(\\n      --default-mono-font-feature-settings,\\n      normal\\n    ); /* 2 */\\n    font-variation-settings: --theme(\\n      --default-mono-font-variation-settings,\\n      normal\\n    ); /* 3 */\\n    font-size: 1em; /* 4 */\\n  }\\n\\n  /*\\n  Add the correct font size in all browsers.\\n*/\\n\\n  small {\\n    font-size: 80%;\\n  }\\n\\n  /*\\n  Prevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\n  sub,\\n  sup {\\n    font-size: 75%;\\n    line-height: 0;\\n    position: relative;\\n    vertical-align: baseline;\\n  }\\n\\n  sub {\\n    bottom: -0.25em;\\n  }\\n\\n  sup {\\n    top: -0.5em;\\n  }\\n\\n  /*\\n  1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n  2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n  3. Remove gaps between table borders by default.\\n*/\\n\\n  table {\\n    text-indent: 0; /* 1 */\\n    border-color: inherit; /* 2 */\\n    border-collapse: collapse; /* 3 */\\n  }\\n\\n  /*\\n  Use the modern Firefox focus style for all focusable elements.\\n*/\\n\\n  :-moz-focusring {\\n    outline: auto;\\n  }\\n\\n  /*\\n  Add the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\n  progress {\\n    vertical-align: baseline;\\n  }\\n\\n  /*\\n  Add the correct display in Chrome and Safari.\\n*/\\n\\n  summary {\\n    display: list-item;\\n  }\\n\\n  /*\\n  Make lists unstyled by default.\\n*/\\n\\n  ol,\\n  ul,\\n  menu {\\n    list-style: none;\\n  }\\n\\n  /*\\n  1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n  2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n      This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\n  img,\\n  svg,\\n  video,\\n  canvas,\\n  audio,\\n  iframe,\\n  embed,\\n  object {\\n    display: block; /* 1 */\\n    vertical-align: middle; /* 2 */\\n  }\\n\\n  /*\\n  Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\n  img,\\n  video {\\n    max-width: 100%;\\n    height: auto;\\n  }\\n\\n  /*\\n  1. Inherit font styles in all browsers.\\n  2. Remove border radius in all browsers.\\n  3. Remove background color in all browsers.\\n  4. Ensure consistent opacity for disabled states in all browsers.\\n*/\\n\\n  button,\\n  input,\\n  select,\\n  optgroup,\\n  textarea,\\n  ::file-selector-button {\\n    font: inherit; /* 1 */\\n    font-feature-settings: inherit; /* 1 */\\n    font-variation-settings: inherit; /* 1 */\\n    letter-spacing: inherit; /* 1 */\\n    color: inherit; /* 1 */\\n    border-radius: 0; /* 2 */\\n    background-color: transparent; /* 3 */\\n    opacity: 1; /* 4 */\\n  }\\n\\n  /*\\n  Restore default font weight.\\n*/\\n\\n  :where(select:is([multiple], [size])) optgroup {\\n    font-weight: bolder;\\n  }\\n\\n  /*\\n  Restore indentation.\\n*/\\n\\n  :where(select:is([multiple], [size])) optgroup option {\\n    padding-inline-start: 20px;\\n  }\\n\\n  /*\\n  Restore space after button.\\n*/\\n\\n  ::file-selector-button {\\n    margin-inline-end: 4px;\\n  }\\n\\n  /*\\n  Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n*/\\n\\n  ::placeholder {\\n    opacity: 1;\\n  }\\n\\n  /*\\n  Set the default placeholder color to a semi-transparent version of the current text color in browsers that do not\\n  crash when using `color-mix(…)` with `currentcolor`. (https://github.com/tailwindlabs/tailwindcss/issues/17194)\\n*/\\n\\n  @supports (not (-webkit-appearance: -apple-pay-button)) /* Not Safari */ or\\n    (contain-intrinsic-size: 1px) /* Safari 17+ */ {\\n    ::placeholder {\\n      color: color-mix(in oklab, currentcolor 50%, transparent);\\n    }\\n  }\\n\\n  /*\\n  Prevent resizing textareas horizontally by default.\\n*/\\n\\n  textarea {\\n    resize: vertical;\\n  }\\n\\n  /*\\n  Remove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n  ::-webkit-search-decoration {\\n    -webkit-appearance: none;\\n  }\\n\\n  /*\\n  1. Ensure date/time inputs have the same height when empty in iOS Safari.\\n  2. Ensure text alignment can be changed on date/time inputs in iOS Safari.\\n*/\\n\\n  ::-webkit-date-and-time-value {\\n    min-height: 1lh; /* 1 */\\n    text-align: inherit; /* 2 */\\n  }\\n\\n  /*\\n  Prevent height from changing on date/time inputs in macOS Safari when the input is set to `display: block`.\\n*/\\n\\n  ::-webkit-datetime-edit {\\n    display: inline-flex;\\n  }\\n\\n  /*\\n  Remove excess padding from pseudo-elements in date/time inputs to ensure consistent height across browsers.\\n*/\\n\\n  ::-webkit-datetime-edit-fields-wrapper {\\n    padding: 0;\\n  }\\n\\n  ::-webkit-datetime-edit,\\n  ::-webkit-datetime-edit-year-field,\\n  ::-webkit-datetime-edit-month-field,\\n  ::-webkit-datetime-edit-day-field,\\n  ::-webkit-datetime-edit-hour-field,\\n  ::-webkit-datetime-edit-minute-field,\\n  ::-webkit-datetime-edit-second-field,\\n  ::-webkit-datetime-edit-millisecond-field,\\n  ::-webkit-datetime-edit-meridiem-field {\\n    padding-block: 0;\\n  }\\n\\n  /*\\n  Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n  :-moz-ui-invalid {\\n    box-shadow: none;\\n  }\\n\\n  /*\\n  Correct the inability to style the border radius in iOS Safari.\\n*/\\n\\n  button,\\n  input:where([type=\\\"button\\\"], [type=\\\"reset\\\"], [type=\\\"submit\\\"]),\\n  ::file-selector-button {\\n    appearance: button;\\n  }\\n\\n  /*\\n  Correct the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n  ::-webkit-inner-spin-button,\\n  ::-webkit-outer-spin-button {\\n    height: auto;\\n  }\\n\\n  /*\\n  Make elements with the HTML hidden attribute stay hidden by default.\\n*/\\n\\n  [hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n    display: none !important;\\n  }\\n}\\n\\n@layer utilities {\\n  @tailwind utilities;\\n}\\n\",\"@property --tw-animation-delay{syntax:\\\"*\\\";inherits:false;initial-value:0s}@property --tw-animation-direction{syntax:\\\"*\\\";inherits:false;initial-value:normal}@property --tw-animation-duration{syntax:\\\"*\\\";inherits:false}@property --tw-animation-fill-mode{syntax:\\\"*\\\";inherits:false;initial-value:none}@property --tw-animation-iteration-count{syntax:\\\"*\\\";inherits:false;initial-value:1}@property --tw-enter-opacity{syntax:\\\"*\\\";inherits:false;initial-value:1}@property --tw-enter-rotate{syntax:\\\"*\\\";inherits:false;initial-value:0}@property --tw-enter-scale{syntax:\\\"*\\\";inherits:false;initial-value:1}@property --tw-enter-translate-x{syntax:\\\"*\\\";inherits:false;initial-value:0}@property --tw-enter-translate-y{syntax:\\\"*\\\";inherits:false;initial-value:0}@property --tw-exit-opacity{syntax:\\\"*\\\";inherits:false;initial-value:1}@property --tw-exit-rotate{syntax:\\\"*\\\";inherits:false;initial-value:0}@property --tw-exit-scale{syntax:\\\"*\\\";inherits:false;initial-value:1}@property --tw-exit-translate-x{syntax:\\\"*\\\";inherits:false;initial-value:0}@property --tw-exit-translate-y{syntax:\\\"*\\\";inherits:false;initial-value:0}@theme inline{--animation-delay-0: 0s; --animation-delay-75: 75ms; --animation-delay-100: .1s; --animation-delay-150: .15s; --animation-delay-200: .2s; --animation-delay-300: .3s; --animation-delay-500: .5s; --animation-delay-700: .7s; --animation-delay-1000: 1s; --animation-repeat-0: 0; --animation-repeat-1: 1; --animation-repeat-infinite: infinite; --animation-direction-normal: normal; --animation-direction-reverse: reverse; --animation-direction-alternate: alternate; --animation-direction-alternate-reverse: alternate-reverse; --animation-fill-mode-none: none; --animation-fill-mode-forwards: forwards; --animation-fill-mode-backwards: backwards; --animation-fill-mode-both: both; --percentage-0: 0; --percentage-5: .05; --percentage-10: .1; --percentage-15: .15; --percentage-20: .2; --percentage-25: .25; --percentage-30: .3; --percentage-35: .35; --percentage-40: .4; --percentage-45: .45; --percentage-50: .5; --percentage-55: .55; --percentage-60: .6; --percentage-65: .65; --percentage-70: .7; --percentage-75: .75; --percentage-80: .8; --percentage-85: .85; --percentage-90: .9; --percentage-95: .95; --percentage-100: 1; --percentage-translate-full: 1; --animate-in: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none); --animate-out: exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none); @keyframes enter { from { opacity: var(--tw-enter-opacity,1); transform: translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0)scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1))rotate(var(--tw-enter-rotate,0)); }}@keyframes exit { to { opacity: var(--tw-exit-opacity,1); transform: translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0)scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1))rotate(var(--tw-exit-rotate,0)); }}--animate-accordion-down: accordion-down var(--tw-animation-duration,var(--tw-duration,.2s))ease-out; --animate-accordion-up: accordion-up var(--tw-animation-duration,var(--tw-duration,.2s))ease-out; --animate-collapsible-down: collapsible-down var(--tw-animation-duration,var(--tw-duration,.2s))ease-out; --animate-collapsible-up: collapsible-up var(--tw-animation-duration,var(--tw-duration,.2s))ease-out; @keyframes accordion-down { from { height: 0; }to { height: var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,var(--kb-accordion-content-height,auto)))); }}@keyframes accordion-up { from { height: var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,var(--kb-accordion-content-height,auto)))); }to { height: 0; }}@keyframes collapsible-down { from { height: 0; }to { height: var(--radix-collapsible-content-height,var(--bits-collapsible-content-height,var(--reka-collapsible-content-height,var(--kb-collapsible-content-height,auto)))); }}@keyframes collapsible-up { from { height: var(--radix-collapsible-content-height,var(--bits-collapsible-content-height,var(--reka-collapsible-content-height,var(--kb-collapsible-content-height,auto)))); }to { height: 0; }}--animate-caret-blink: caret-blink 1.25s ease-out infinite; @keyframes caret-blink { 0%,70%,100% { opacity: 1; }20%,50% { opacity: 0; }}}@utility animation-duration-*{--tw-animation-duration: calc(--value(number)*1ms); --tw-animation-duration: --value(--animation-duration-*,[duration],\\\"initial\\\",[*]); animation-duration: calc(--value(number)*1ms); animation-duration: --value(--animation-duration-*,[duration],\\\"initial\\\",[*]);}@utility delay-*{animation-delay: calc(--value(number)*1ms); animation-delay: --value(--animation-delay-*,[duration],\\\"initial\\\",[*]); --tw-animation-delay: calc(--value(number)*1ms); --tw-animation-delay: --value(--animation-delay-*,[duration],\\\"initial\\\",[*]);}@utility repeat-*{animation-iteration-count: --value(--animation-repeat-*,number,\\\"initial\\\",[*]); --tw-animation-iteration-count: --value(--animation-repeat-*,number,\\\"initial\\\",[*]);}@utility direction-*{animation-direction: --value(--animation-direction-*,\\\"initial\\\",[*]); --tw-animation-direction: --value(--animation-direction-*,\\\"initial\\\",[*]);}@utility fill-mode-*{animation-fill-mode: --value(--animation-fill-mode-*,\\\"initial\\\",[*]); --tw-animation-fill-mode: --value(--animation-fill-mode-*,\\\"initial\\\",[*]);}@utility running{animation-play-state: running;}@utility paused{animation-play-state: paused;}@utility play-state-*{animation-play-state: --value(\\\"initial\\\",[*]);}@utility fade-in{--tw-enter-opacity: 0;}@utility fade-in-*{--tw-enter-opacity: calc(--value(number)/100); --tw-enter-opacity: --value(--percentage-*,[*]);}@utility fade-out{--tw-exit-opacity: 0;}@utility fade-out-*{--tw-exit-opacity: calc(--value(number)/100); --tw-exit-opacity: --value(--percentage-*,[*]);}@utility zoom-in{--tw-enter-scale: 0;}@utility zoom-in-*{--tw-enter-scale: calc(--value(number)*1%); --tw-enter-scale: calc(--value(ratio)); --tw-enter-scale: --value(--percentage-*,[*]);}@utility -zoom-in-*{--tw-enter-scale: calc(--value(number)*-1%); --tw-enter-scale: calc(--value(ratio)*-1); --tw-enter-scale: --value(--percentage-*,[*]);}@utility zoom-out{--tw-exit-scale: 0;}@utility zoom-out-*{--tw-exit-scale: calc(--value(number)*1%); --tw-exit-scale: calc(--value(ratio)); --tw-exit-scale: --value(--percentage-*,[*]);}@utility -zoom-out-*{--tw-exit-scale: calc(--value(number)*-1%); --tw-exit-scale: calc(--value(ratio)*-1); --tw-exit-scale: --value(--percentage-*,[*]);}@utility spin-in{--tw-enter-rotate: 30deg;}@utility spin-in-*{--tw-enter-rotate: calc(--value(number)*1deg); --tw-enter-rotate: calc(--value(ratio)*360deg); --tw-enter-rotate: --value(--rotate-*,[*]);}@utility -spin-in{--tw-enter-rotate: -30deg;}@utility -spin-in-*{--tw-enter-rotate: calc(--value(number)*-1deg); --tw-enter-rotate: calc(--value(ratio)*-360deg); --tw-enter-rotate: --value(--rotate-*,[*]);}@utility spin-out{--tw-exit-rotate: 30deg;}@utility spin-out-*{--tw-exit-rotate: calc(--value(number)*1deg); --tw-exit-rotate: calc(--value(ratio)*360deg); --tw-exit-rotate: --value(--rotate-*,[*]);}@utility -spin-out{--tw-exit-rotate: -30deg;}@utility -spin-out-*{--tw-exit-rotate: calc(--value(number)*-1deg); --tw-exit-rotate: calc(--value(ratio)*-360deg); --tw-exit-rotate: --value(--rotate-*,[*]);}@utility slide-in-from-top{--tw-enter-translate-y: -100%;}@utility slide-in-from-top-*{--tw-enter-translate-y: calc(--value(integer)*var(--spacing)*-1); --tw-enter-translate-y: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-enter-translate-y: calc(--value(ratio)*-100%); --tw-enter-translate-y: calc(--value(--translate-*,[percentage],[length])*-1);}@utility slide-in-from-bottom{--tw-enter-translate-y: 100%;}@utility slide-in-from-bottom-*{--tw-enter-translate-y: calc(--value(integer)*var(--spacing)); --tw-enter-translate-y: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-enter-translate-y: calc(--value(ratio)*100%); --tw-enter-translate-y: --value(--translate-*,[percentage],[length]);}@utility slide-in-from-left{--tw-enter-translate-x: -100%;}@utility slide-in-from-left-*{--tw-enter-translate-x: calc(--value(integer)*var(--spacing)*-1); --tw-enter-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-enter-translate-x: calc(--value(ratio)*-100%); --tw-enter-translate-x: calc(--value(--translate-*,[percentage],[length])*-1);}@utility slide-in-from-right{--tw-enter-translate-x: 100%;}@utility slide-in-from-right-*{--tw-enter-translate-x: calc(--value(integer)*var(--spacing)); --tw-enter-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-enter-translate-x: calc(--value(ratio)*100%); --tw-enter-translate-x: --value(--translate-*,[percentage],[length]);}@utility slide-in-from-start{&:dir(ltr){ --tw-enter-translate-x: -100%; }&:dir(rtl){ --tw-enter-translate-x: 100%; }}@utility slide-in-from-start-*{&:where(:dir(ltr),[dir=\\\"ltr\\\"],[dir=\\\"ltr\\\"]*){ --tw-enter-translate-x: calc(--value(integer)*var(--spacing)*-1); --tw-enter-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-enter-translate-x: calc(--value(ratio)*-100%); --tw-enter-translate-x: calc(--value(--translate-*,[percentage],[length])*-1); }&:where(:dir(rtl),[dir=\\\"rtl\\\"],[dir=\\\"rtl\\\"]*){ --tw-enter-translate-x: calc(--value(integer)*var(--spacing)); --tw-enter-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-enter-translate-x: calc(--value(ratio)*100%); --tw-enter-translate-x: --value(--translate-*,[percentage],[length]); }}@utility slide-in-from-end{&:dir(ltr){ --tw-enter-translate-x: 100%; }&:dir(rtl){ --tw-enter-translate-x: -100%; }}@utility slide-in-from-end-*{&:where(:dir(ltr),[dir=\\\"ltr\\\"],[dir=\\\"ltr\\\"]*){ --tw-enter-translate-x: calc(--value(integer)*var(--spacing)); --tw-enter-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-enter-translate-x: calc(--value(ratio)*100%); --tw-enter-translate-x: --value(--translate-*,[percentage],[length]); }&:where(:dir(rtl),[dir=\\\"rtl\\\"],[dir=\\\"rtl\\\"]*){ --tw-enter-translate-x: calc(--value(integer)*var(--spacing)*-1); --tw-enter-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-enter-translate-x: calc(--value(ratio)*-100%); --tw-enter-translate-x: calc(--value(--translate-*,[percentage],[length])*-1); }}@utility slide-out-to-top{--tw-exit-translate-y: -100%;}@utility slide-out-to-top-*{--tw-exit-translate-y: calc(--value(integer)*var(--spacing)*-1); --tw-exit-translate-y: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-exit-translate-y: calc(--value(ratio)*-100%); --tw-exit-translate-y: calc(--value(--translate-*,[percentage],[length])*-1);}@utility slide-out-to-bottom{--tw-exit-translate-y: 100%;}@utility slide-out-to-bottom-*{--tw-exit-translate-y: calc(--value(integer)*var(--spacing)); --tw-exit-translate-y: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-exit-translate-y: calc(--value(ratio)*100%); --tw-exit-translate-y: --value(--translate-*,[percentage],[length]);}@utility slide-out-to-left{--tw-exit-translate-x: -100%;}@utility slide-out-to-left-*{--tw-exit-translate-x: calc(--value(integer)*var(--spacing)*-1); --tw-exit-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-exit-translate-x: calc(--value(ratio)*-100%); --tw-exit-translate-x: calc(--value(--translate-*,[percentage],[length])*-1);}@utility slide-out-to-right{--tw-exit-translate-x: 100%;}@utility slide-out-to-right-*{--tw-exit-translate-x: calc(--value(integer)*var(--spacing)); --tw-exit-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-exit-translate-x: calc(--value(ratio)*100%); --tw-exit-translate-x: --value(--translate-*,[percentage],[length]);}@utility slide-out-to-start{&:dir(ltr){ --tw-exit-translate-x: -100%; }&:dir(rtl){ --tw-exit-translate-x: 100%; }}@utility slide-out-to-start-*{&:where(:dir(ltr),[dir=\\\"ltr\\\"],[dir=\\\"ltr\\\"]*){ --tw-exit-translate-x: calc(--value(integer)*var(--spacing)*-1); --tw-exit-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-exit-translate-x: calc(--value(ratio)*-100%); --tw-exit-translate-x: calc(--value(--translate-*,[percentage],[length])*-1); }&:where(:dir(rtl),[dir=\\\"rtl\\\"],[dir=\\\"rtl\\\"]*){ --tw-exit-translate-x: calc(--value(integer)*var(--spacing)); --tw-exit-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-exit-translate-x: calc(--value(ratio)*100%); --tw-exit-translate-x: --value(--translate-*,[percentage],[length]); }}@utility slide-out-to-end{&:dir(ltr){ --tw-exit-translate-x: 100%; }&:dir(rtl){ --tw-exit-translate-x: -100%; }}@utility slide-out-to-end-*{&:where(:dir(ltr),[dir=\\\"ltr\\\"],[dir=\\\"ltr\\\"]*){ --tw-exit-translate-x: calc(--value(integer)*var(--spacing)); --tw-exit-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-exit-translate-x: calc(--value(ratio)*100%); --tw-exit-translate-x: --value(--translate-*,[percentage],[length]); }&:where(:dir(rtl),[dir=\\\"rtl\\\"],[dir=\\\"rtl\\\"]*){ --tw-exit-translate-x: calc(--value(integer)*var(--spacing)*-1); --tw-exit-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-exit-translate-x: calc(--value(ratio)*-100%); --tw-exit-translate-x: calc(--value(--translate-*,[percentage],[length])*-1); }}\",\"@import \\\"tailwindcss\\\";\\n@import \\\"tw-animate-css\\\";\\n\\n@custom-variant dark (&:is(.dark *));\\n\\n:root {\\n  --background: #ffffff;\\n  --foreground: #171717;\\n  --card: oklch(1 0 0);\\n  --card-foreground: oklch(0.145 0 0);\\n  --popover: oklch(1 0 0);\\n  --popover-foreground: oklch(0.145 0 0);\\n  /* --primary: oklch(0.205 0 0); */\\n\\n  --primary: oklch(24.37% 0.0951 286.37);\\n  --primary-foreground: oklch(0.985 0 0);\\n  /* --secondary: oklch(0.97 0 0); */\\n  --secondary: oklch(37.34% 0.1397 315.97);\\n\\n  --secondary-foreground: oklch(0.205 0 0);\\n  --muted: oklch(0.97 0 0);\\n  --muted-foreground: oklch(0.556 0 0);\\n  --accent: oklch(0.97 0 0);\\n  --accent-foreground: oklch(0.205 0 0);\\n  --destructive: oklch(0.577 0.245 27.325);\\n  --destructive-foreground: oklch(0.577 0.245 27.325);\\n  --border: oklch(0.922 0 0);\\n  --input: oklch(0.922 0 0);\\n  --ring: oklch(0.708 0 0);\\n  --chart-1: oklch(0.646 0.222 41.116);\\n  --chart-2: oklch(0.6 0.118 184.704);\\n  --chart-3: oklch(0.398 0.07 227.392);\\n  --chart-4: oklch(0.828 0.189 84.429);\\n  --chart-5: oklch(0.769 0.188 70.08);\\n  --radius: 0.625rem;\\n  --sidebar: oklch(0.985 0 0);\\n  --sidebar-foreground: oklch(0.145 0 0);\\n  --sidebar-primary: oklch(0.205 0 0);\\n  --sidebar-primary-foreground: oklch(0.985 0 0);\\n  --sidebar-accent: oklch(0.97 0 0);\\n  --sidebar-accent-foreground: oklch(0.205 0 0);\\n  --sidebar-border: oklch(0.922 0 0);\\n  --sidebar-ring: oklch(0.708 0 0);\\n\\n  --dccpink: oklch(0.54 0.216689 5.2);\\n  --dccblue: oklch(0.24 0.0951 286.37);\\n  --dcclightblue: oklch(0.64 0.1154 218.6);\\n  --dccviolet: oklch(0.45 0.1883 326.95);\\n  --dccpurple: oklch(0.37 0.1397 315.97);\\n  --dcclightgrey: oklch(0.7 0.0146 134.93);\\n  --dccdarkgrey: oklch(0.44 0.0031 228.84);\\n  --dccyellow: oklch(0.87 0.1768 90.38);\\n  --dccgreen: oklch(0.75 0.1806 124.9);\\n  --dccgrey: oklch(0.7 0.0146 134.93);\\n  --dccorange: oklch(0.75 0.1674 64.79);\\n  --dcclightorange: oklch(0.83 0.1464 73.9);\\n}\\n\\n@theme inline {\\n  --color-background: var(--background);\\n  --color-foreground: var(--foreground);\\n  --font-sans: var(--font-geist-sans);\\n  --font-mono: var(--font-geist-mono);\\n  --color-sidebar-ring: var(--sidebar-ring);\\n  --color-sidebar-border: var(--sidebar-border);\\n  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);\\n  --color-sidebar-accent: var(--sidebar-accent);\\n  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);\\n  --color-sidebar-primary: var(--sidebar-primary);\\n  --color-sidebar-foreground: var(--sidebar-foreground);\\n  --color-sidebar: var(--sidebar);\\n  --color-chart-5: var(--chart-5);\\n  --color-chart-4: var(--chart-4);\\n  --color-chart-3: var(--chart-3);\\n  --color-chart-2: var(--chart-2);\\n  --color-chart-1: var(--chart-1);\\n  --color-ring: var(--ring);\\n  --color-input: var(--input);\\n  --color-border: var(--border);\\n  --color-destructive-foreground: var(--destructive-foreground);\\n  --color-destructive: var(--destructive);\\n  --color-accent-foreground: var(--accent-foreground);\\n  --color-accent: var(--accent);\\n  --color-muted-foreground: var(--muted-foreground);\\n  --color-muted: var(--muted);\\n  --color-secondary-foreground: var(--secondary-foreground);\\n  --color-secondary: var(--secondary);\\n  --color-primary-foreground: var(--primary-foreground);\\n  --color-primary: var(--primary);\\n  --color-popover-foreground: var(--popover-foreground);\\n  --color-popover: var(--popover);\\n  --color-card-foreground: var(--card-foreground);\\n  --color-card: var(--card);\\n  --radius-sm: calc(var(--radius) - 4px);\\n  --radius-md: calc(var(--radius) - 2px);\\n  --radius-lg: var(--radius);\\n  --radius-xl: calc(var(--radius) + 4px);\\n\\n  --color-dccpink: var(--dccpink);\\n  --color-dccblue: var(--dccblue);\\n  --color-dcclightblue: var(--dcclightblue);\\n  --color-dccviolet: var(--dccviolet);\\n  --color-dccpurple: var(--dccpurple);\\n  --color-dcclightgrey: var(--dcclightgrey);\\n  --color-dccyellow: var(--dccyellow);\\n  --color-dccgreen: var(--dccgreen);\\n  --color-dccgrey: var(--dccgrey);\\n  --color-dccorange: var(--dccorange);\\n  --color-dcclightorange: var(--dcclightorange);\\n  --color-dccdarkgrey: var(--dccdarkgrey);\\n}\\n\\n@media (prefers-color-scheme: dark) {\\n  :root {\\n    /* --background: #ededed; */\\n    /* --background: #0a0a0a; */\\n    /* --foreground: #ededed; */\\n  }\\n}\\n\\nbody {\\n  background: var(--background);\\n  color: var(--foreground);\\n  font-family: Arial, Helvetica, sans-serif;\\n}\\n\\n.dark {\\n  --background: oklch(0.145 0 0);\\n  --foreground: oklch(0.985 0 0);\\n  --card: oklch(0.145 0 0);\\n  --card-foreground: oklch(0.985 0 0);\\n  --popover: oklch(0.145 0 0);\\n  --popover-foreground: oklch(0.985 0 0);\\n  --primary: oklch(0.985 0 0);\\n  --primary-foreground: oklch(0.205 0 0);\\n  --secondary: oklch(0.269 0 0);\\n  --secondary-foreground: oklch(0.985 0 0);\\n  --muted: oklch(0.269 0 0);\\n  --muted-foreground: oklch(0.708 0 0);\\n  --accent: oklch(0.269 0 0);\\n  --accent-foreground: oklch(0.985 0 0);\\n  --destructive: oklch(0.396 0.141 25.723);\\n  --destructive-foreground: oklch(0.637 0.237 25.331);\\n  --border: oklch(0.269 0 0);\\n  --input: oklch(0.269 0 0);\\n  --ring: oklch(0.439 0 0);\\n  --chart-1: oklch(0.488 0.243 264.376);\\n  --chart-2: oklch(0.696 0.17 162.48);\\n  --chart-3: oklch(0.769 0.188 70.08);\\n  --chart-4: oklch(0.627 0.265 303.9);\\n  --chart-5: oklch(0.645 0.246 16.439);\\n  --sidebar: oklch(0.205 0 0);\\n  --sidebar-foreground: oklch(0.985 0 0);\\n  --sidebar-primary: oklch(0.488 0.243 264.376);\\n  --sidebar-primary-foreground: oklch(0.985 0 0);\\n  --sidebar-accent: oklch(0.269 0 0);\\n  --sidebar-accent-foreground: oklch(0.985 0 0);\\n  --sidebar-border: oklch(0.269 0 0);\\n  --sidebar-ring: oklch(0.439 0 0);\\n}\\n\\n@layer base {\\n  * {\\n    @apply border-border outline-ring/50;\\n  }\\n  body {\\n    @apply bg-background text-foreground;\\n  }\\n}\\n\\n@layer base {\\n  :root {\\n    --sidebar-background: 0 0% 98%;\\n    --sidebar-foreground: 240 5.3% 26.1%;\\n    --sidebar-primary: 240 5.9% 10%;\\n    --sidebar-primary-foreground: 0 0% 98%;\\n    --sidebar-accent: 240 4.8% 95.9%;\\n    --sidebar-accent-foreground: 240 5.9% 10%;\\n    --sidebar-border: 220 13% 91%;\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\n  }\\n\\n  .dark {\\n    --sidebar-background: 240 5.9% 10%;\\n    --sidebar-foreground: 240 4.8% 95.9%;\\n    --sidebar-primary: 224.3 76.3% 48%;\\n    --sidebar-primary-foreground: 0 0% 100%;\\n    --sidebar-accent: 240 3.7% 15.9%;\\n    --sidebar-accent-foreground: 240 4.8% 95.9%;\\n    --sidebar-border: 240 3.7% 15.9%;\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css\n"));

/***/ })

});