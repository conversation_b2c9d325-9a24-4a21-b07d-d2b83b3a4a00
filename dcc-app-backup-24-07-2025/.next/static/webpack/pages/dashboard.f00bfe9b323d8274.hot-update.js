"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "(pages-dir-browser)/./core/components/app-sidebar.jsx":
/*!*****************************************!*\
  !*** ./core/components/app-sidebar.jsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppSidebar: () => (/* binding */ AppSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookUser_Cog_LayoutDashboard_Store_UserSquare_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookUser,Cog,LayoutDashboard,Store,UserSquare!=!lucide-react */ \"(pages-dir-browser)/__barrel_optimize__?names=BookUser,Cog,LayoutDashboard,Store,UserSquare!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @core/components/ui/sidebar */ \"(pages-dir-browser)/./core/components/ui/sidebar.jsx\");\n/* harmony import */ var _core_components_ui_team_switcher__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @core/components/ui/team-switcher */ \"(pages-dir-browser)/./core/components/ui/team-switcher.jsx\");\n/* harmony import */ var _core_components_ui_nav_main__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @core/components/ui/nav-main */ \"(pages-dir-browser)/./core/components/ui/nav-main.jsx\");\n/* harmony import */ var _core_components_ui_nav_projects__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @core/components/ui/nav-projects */ \"(pages-dir-browser)/./core/components/ui/nav-projects.jsx\");\n/* harmony import */ var _core_components_ui_nav_role__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @core/components/ui/nav-role */ \"(pages-dir-browser)/./core/components/ui/nav-role.jsx\");\n/* harmony import */ var _core_components_ui_nav_dashboard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @core/components/ui/nav-dashboard */ \"(pages-dir-browser)/./core/components/ui/nav-dashboard.jsx\");\n/* harmony import */ var _core_components_ui_nav_resources__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @core/components/ui/nav-resources */ \"(pages-dir-browser)/./core/components/ui/nav-resources.jsx\");\n/* harmony import */ var _core_components_ui_nav_user__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @core/components/ui/nav-user */ \"(pages-dir-browser)/./core/components/ui/nav-user.jsx\");\n/* harmony import */ var _core_components_ui_button__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @core/components/ui/button */ \"(pages-dir-browser)/./core/components/ui/button.jsx\");\n/* harmony import */ var _core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @core/components/ui/breadcrumb */ \"(pages-dir-browser)/./core/components/ui/breadcrumb.jsx\");\n/* harmony import */ var _core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @core/components/ui/select */ \"(pages-dir-browser)/./core/components/ui/select.jsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Menu items.\nconst items = [\n    {\n        title: \"Skills Library\",\n        url: \"#\",\n        icon: _barrel_optimize_names_BookUser_Cog_LayoutDashboard_Store_UserSquare_lucide_react__WEBPACK_IMPORTED_MODULE_13__.LayoutDashboard,\n        isActive: true,\n        items: [\n            {\n                title: \"History\",\n                url: \"#\"\n            },\n            {\n                title: \"Starred\",\n                url: \"#\"\n            },\n            {\n                title: \"Settings\",\n                url: \"#\"\n            }\n        ]\n    }\n];\nconst data = {\n    user: {\n        name: \"Richard Stephens\",\n        email: \"<EMAIL>\",\n        avatar: \"/avatars/user.jpg\"\n    },\n    // teams: [\n    //   {\n    //     name: \"Acme Inc\",\n    //     logo: GalleryVerticalEnd,\n    //     plan: \"Enterprise\",\n    //   },\n    //   {\n    //     name: \"Acme Corp.\",\n    //     logo: AudioWaveform,\n    //     plan: \"Startup\",\n    //   },\n    //   {\n    //     name: \"Evil Corp.\",\n    //     logo: Command,\n    //     plan: \"Free\",\n    //   },\n    // ],\n    dashboard: [\n        {\n            name: \"Dashboard\",\n            url: \"/dashboard\",\n            icon: _barrel_optimize_names_BookUser_Cog_LayoutDashboard_Store_UserSquare_lucide_react__WEBPACK_IMPORTED_MODULE_13__.LayoutDashboard\n        }\n    ],\n    navMain2: [\n        {\n            name: \"Dashboard\",\n            url: \"/dashboard\",\n            icon: _barrel_optimize_names_BookUser_Cog_LayoutDashboard_Store_UserSquare_lucide_react__WEBPACK_IMPORTED_MODULE_13__.LayoutDashboard\n        }\n    ],\n    navMain: [\n        {\n            title: \"Behavioural skills\",\n            url: \"#\",\n            icon: _barrel_optimize_names_BookUser_Cog_LayoutDashboard_Store_UserSquare_lucide_react__WEBPACK_IMPORTED_MODULE_13__.UserSquare,\n            isActive: false,\n            items: [\n                {\n                    title: \"Strategic Thinking\",\n                    url: \"/strategic-thinking\"\n                },\n                {\n                    title: \"Purposeful Planning\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Shaping Solutions\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Customer Focus\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Agile and Adaptable\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Engage and Influence\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Deliver Results\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Collaborate Openly\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Trust and Integrity\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Develop Self\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Enable Performance\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Develop Others\",\n                    url: \"#\"\n                }\n            ]\n        },\n        {\n            title: \"Technical skills\",\n            url: \"#\",\n            icon: _barrel_optimize_names_BookUser_Cog_LayoutDashboard_Store_UserSquare_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Cog,\n            items: [\n                {\n                    title: \"Business Continuity and Disaster Recovery\",\n                    url: \"/business-continuity\"\n                },\n                {\n                    title: \"Data Analytics and Insights \",\n                    url: \"#\"\n                },\n                {\n                    title: \"Programme Management\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Certificate Management\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Development Lifecycle \",\n                    url: \"#\"\n                },\n                {\n                    title: \"Risk Management\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Change Management\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Enterprise System Architecture\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Supplier Management\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Commercial Acumen\",\n                    url: \"#\"\n                },\n                {\n                    title: \"External Environment Scanning\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Sustainability and Environment\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Compliance and Regulatory Assurance\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Governance \",\n                    url: \"#\"\n                },\n                {\n                    title: \"Talent Management\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Communications \",\n                    url: \"#\"\n                },\n                {\n                    title: \"Incident Response Lifecycle\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Technology Assurance and Testing\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Continuous Improvement\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Infrastructure and Cloud Computing\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Threat Intelligence\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Contract Management \",\n                    url: \"#\"\n                },\n                {\n                    title: \"Legal Counsel\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Customer Experience Design\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Portfolio Management\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Customer Operations \",\n                    url: \"#\"\n                },\n                {\n                    title: \"Procurement\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Critical Thinking \",\n                    url: \"#\"\n                },\n                {\n                    title: \"Project Management\",\n                    url: \"#\"\n                }\n            ]\n        }\n    ],\n    projects: [],\n    role: [\n        {\n            name: \"Roles\",\n            url: \"/roles\",\n            icon: _barrel_optimize_names_BookUser_Cog_LayoutDashboard_Store_UserSquare_lucide_react__WEBPACK_IMPORTED_MODULE_13__.BookUser\n        }\n    ],\n    resources: [\n        {\n            name: \"Talent Marketplace\",\n            url: \"/talent-marketplace\",\n            icon: _barrel_optimize_names_BookUser_Cog_LayoutDashboard_Store_UserSquare_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Store\n        }\n    ]\n};\nconst algorithm = [\n    \"Cyber Security Operations\",\n    \"Security Architecture and Assurance\"\n];\nconst language = [\n    \"Security Compliance, Risk and Resilience\",\n    \"Security, Demand, Capability and Awareness\"\n];\nfunction AppSidebar(param) {\n    let { userData } = param;\n    _s();\n    const { state, open, setOpen, openMobile, setOpenMobile, isMobile, toggleSidebar } = (0,_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.useSidebar)();\n    const [selected, setSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [teamSelected, setTeamSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSkills, setShowSkills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [typeSelected, setTypeSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const changeSelectOptionHandler = (value)=>{\n        setSelected(value);\n        setShowSkills(false);\n    };\n    const teamSelectOptionHandler = (value)=>{\n        setTeamSelected(value);\n        setShowSkills(true);\n    };\n    const typeSelector = (value)=>{\n        props.handleShowSkills(value);\n    };\n    /* --- DEBUG --- */ console.log(\"userData\", userData);\n    /* --- DEBUG --- */ /** Type variable to store different array for different dropdown */ let type = null;\n    /** This will be used to create set of options that user will see */ let options = null;\n    /** Setting Type variable according to dropdown */ if (selected === \"Security\") {\n        type = algorithm;\n    } else if (selected === \"Another Security\") {\n        type = language;\n    }\n    /** If \"Type\" is null or undefined then options will be null,\n   * otherwise it will create a options iterable based on our array\n   */ if (type) {\n        options = type.map((el)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                value: el,\n                children: el\n            }, el, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                lineNumber: 445,\n                columnNumber: 7\n            }, this));\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.Sidebar, {\n        collapsible: \"offcanvas\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.SidebarHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_team_switcher__WEBPACK_IMPORTED_MODULE_3__.TeamSwitcher, {\n                    teams: data.teams\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                    lineNumber: 454,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                lineNumber: 453,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.SidebarContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.SidebarGroup, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                        lineNumber: 457,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_nav_dashboard__WEBPACK_IMPORTED_MODULE_7__.NavDashboard, {\n                        projects: data.dashboard\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                        lineNumber: 458,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_nav_main__WEBPACK_IMPORTED_MODULE_4__.NavMain, {\n                        items: data.navMain\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                        lineNumber: 459,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.SidebarGroup, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                        lineNumber: 461,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                lineNumber: 456,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.SidebarFooter, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_nav_user__WEBPACK_IMPORTED_MODULE_9__.NavUser, {\n                    user: userData\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                    lineNumber: 464,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                lineNumber: 463,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.SidebarRail, {}, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                lineNumber: 466,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n        lineNumber: 452,\n        columnNumber: 5\n    }, this);\n}\n_s(AppSidebar, \"aMTQ7+GZT+4mmFaQavXTNoT1a/U=\", false, function() {\n    return [\n        _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.useSidebar\n    ];\n});\n_c = AppSidebar;\nvar _c;\n$RefreshReg$(_c, \"AppSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./core/components/app-sidebar.jsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/__barrel_optimize__?names=BookUser,Cog,LayoutDashboard,Store,UserSquare!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!**************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BookUser,Cog,LayoutDashboard,Store,UserSquare!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BookUser: () => (/* reexport safe */ _icons_book_user_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Cog: () => (/* reexport safe */ _icons_cog_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   LayoutDashboard: () => (/* reexport safe */ _icons_layout_dashboard_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Store: () => (/* reexport safe */ _icons_store_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   UserSquare: () => (/* reexport safe */ _icons_square_user_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_book_user_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/book-user.js */ \"(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/book-user.js\");\n/* harmony import */ var _icons_cog_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/cog.js */ \"(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/cog.js\");\n/* harmony import */ var _icons_layout_dashboard_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/layout-dashboard.js */ \"(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _icons_store_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/store.js */ \"(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _icons_square_user_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/square-user.js */ \"(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/square-user.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUJvb2tVc2VyLENvZyxMYXlvdXREYXNoYm9hcmQsU3RvcmUsVXNlclNxdWFyZSE9IS4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFDMEQ7QUFDWDtBQUN5QjtBQUNyQiIsInNvdXJjZXMiOlsiL1VzZXJzL3JpamFzdC9fX2RhdGEvX19kZXYvRXF1YWxpdGFsL2Rldi9kY2MvZGNjLWFwcC9kY2MtYXBwL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCb29rVXNlciB9IGZyb20gXCIuL2ljb25zL2Jvb2stdXNlci5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENvZyB9IGZyb20gXCIuL2ljb25zL2NvZy5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIExheW91dERhc2hib2FyZCB9IGZyb20gXCIuL2ljb25zL2xheW91dC1kYXNoYm9hcmQuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTdG9yZSB9IGZyb20gXCIuL2ljb25zL3N0b3JlLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVXNlclNxdWFyZSB9IGZyb20gXCIuL2ljb25zL3NxdWFyZS11c2VyLmpzXCIiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/__barrel_optimize__?names=BookUser,Cog,LayoutDashboard,Store,UserSquare!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n"));

/***/ })

});