"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "(pages-dir-browser)/./core/components/ui/nav-user.jsx":
/*!*****************************************!*\
  !*** ./core/components/ui/nav-user.jsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavUser: () => (/* binding */ NavUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_EllipsisVertical_LogOut_Settings_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BadgeCheck,Bell,ChevronsUpDown,EllipsisVertical,LogOut,Settings,Sparkles!=!lucide-react */ \"(pages-dir-browser)/__barrel_optimize__?names=BadgeCheck,Bell,ChevronsUpDown,EllipsisVertical,LogOut,Settings,Sparkles!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _core_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @core/components/ui/avatar */ \"(pages-dir-browser)/./core/components/ui/avatar.jsx\");\n/* harmony import */ var _core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @core/components/ui/dropdown-menu */ \"(pages-dir-browser)/./core/components/ui/dropdown-menu.jsx\");\n/* harmony import */ var _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @core/components/ui/sidebar */ \"(pages-dir-browser)/./core/components/ui/sidebar.jsx\");\n/* __next_internal_client_entry_do_not_use__ NavUser auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction NavUser(param) {\n    let { user } = param;\n    _s();\n    const { isMobile } = (0,_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.useSidebar)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenu, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuItem, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenu, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuTrigger, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuButton, {\n                            size: \"lg\",\n                            className: \"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.Avatar, {\n                                    className: \"h-8 w-8 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.AvatarImage, {\n                                            src: user.avatar,\n                                            alt: user.first_name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.AvatarFallback, {\n                                            className: \"rounded-l-full bg-primary text-white\",\n                                            children: \"CN\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid flex-1 text-left text-sm leading-tight\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate font-semibold\",\n                                            children: user.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate text-xs\",\n                                            children: user.email\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_EllipsisVertical_LogOut_Settings_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__.EllipsisVertical, {\n                                    className: \"ml-auto size-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                            lineNumber: 42,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuContent, {\n                        className: \"w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg\",\n                        side: isMobile ? \"bottom\" : \"right\",\n                        align: \"end\",\n                        sideOffset: 4,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuLabel, {\n                                className: \"p-0 font-normal\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 px-1 py-1.5 text-left text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.Avatar, {\n                                            className: \"h-8 w-8 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.AvatarImage, {\n                                                    src: user.avatar,\n                                                    alt: user.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.AvatarFallback, {\n                                                    className: \"rounded-lg\",\n                                                    children: \"CN\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid flex-1 text-left text-sm leading-tight\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"truncate font-semibold\",\n                                                    children: user.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"truncate text-xs\",\n                                                    children: user.email\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuSeparator, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuGroup, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_EllipsisVertical_LogOut_Settings_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__.BadgeCheck, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Account\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_EllipsisVertical_LogOut_Settings_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Settings, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Settings\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_EllipsisVertical_LogOut_Settings_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Bell, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Notifications\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuSeparator, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_EllipsisVertical_LogOut_Settings_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__.LogOut, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Log out\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_s(NavUser, \"7bt3Tpt+2g9LjYXqO6MQJeduxl4=\", false, function() {\n    return [\n        _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.useSidebar\n    ];\n});\n_c = NavUser;\nvar _c;\n$RefreshReg$(_c, \"NavUser\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL2NvcmUvY29tcG9uZW50cy91aS9uYXYtdXNlci5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBVXNCO0FBTWM7QUFTTztBQU1OO0FBRTlCLFNBQVNxQixRQUFRLEtBQVE7UUFBUixFQUFFQyxJQUFJLEVBQUUsR0FBUjs7SUFDdEIsTUFBTSxFQUFFQyxRQUFRLEVBQUUsR0FBR0gsdUVBQVVBO0lBRS9CLHFCQUNFLDhEQUFDSCxvRUFBV0E7a0JBQ1YsNEVBQUNFLHdFQUFlQTtzQkFDZCw0RUFBQ1QsMkVBQVlBOztrQ0FDWCw4REFBQ00sa0ZBQW1CQTt3QkFBQ1EsT0FBTztrQ0FDMUIsNEVBQUNOLDBFQUFpQkE7NEJBQ2hCTyxNQUFLOzRCQUNMQyxXQUFVOzs4Q0FFViw4REFBQ25CLDhEQUFNQTtvQ0FBQ21CLFdBQVU7O3NEQUNoQiw4REFBQ2pCLG1FQUFXQTs0Q0FBQ2tCLEtBQUtMLEtBQUtNLE1BQU07NENBQUVDLEtBQUtQLEtBQUtRLFVBQVU7Ozs7OztzREFDbkQsOERBQUN0QixzRUFBY0E7NENBQUNrQixXQUFVO3NEQUF1Qzs7Ozs7Ozs7Ozs7OzhDQUluRSw4REFBQ0s7b0NBQUlMLFdBQVU7O3NEQUNiLDhEQUFDTTs0Q0FBS04sV0FBVTtzREFBMEJKLEtBQUtXLElBQUk7Ozs7OztzREFDbkQsOERBQUNEOzRDQUFLTixXQUFVO3NEQUFvQkosS0FBS1ksS0FBSzs7Ozs7Ozs7Ozs7OzhDQUVoRCw4REFBQy9CLDBKQUFnQkE7b0NBQUN1QixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FHaEMsOERBQUNmLGtGQUFtQkE7d0JBQ2xCZSxXQUFVO3dCQUNWUyxNQUFNWixXQUFXLFdBQVc7d0JBQzVCYSxPQUFNO3dCQUNOQyxZQUFZOzswQ0FFWiw4REFBQ3ZCLGdGQUFpQkE7Z0NBQUNZLFdBQVU7MENBQzNCLDRFQUFDSztvQ0FBSUwsV0FBVTs7c0RBQ2IsOERBQUNuQiw4REFBTUE7NENBQUNtQixXQUFVOzs4REFDaEIsOERBQUNqQixtRUFBV0E7b0RBQUNrQixLQUFLTCxLQUFLTSxNQUFNO29EQUFFQyxLQUFLUCxLQUFLVyxJQUFJOzs7Ozs7OERBQzdDLDhEQUFDekIsc0VBQWNBO29EQUFDa0IsV0FBVTs4REFBYTs7Ozs7Ozs7Ozs7O3NEQUV6Qyw4REFBQ0s7NENBQUlMLFdBQVU7OzhEQUNiLDhEQUFDTTtvREFBS04sV0FBVTs4REFBMEJKLEtBQUtXLElBQUk7Ozs7Ozs4REFDbkQsOERBQUNEO29EQUFLTixXQUFVOzhEQUFvQkosS0FBS1ksS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBV3BELDhEQUFDbkIsb0ZBQXFCQTs7Ozs7MENBQ3RCLDhEQUFDSCxnRkFBaUJBOztrREFDaEIsOERBQUNDLCtFQUFnQkE7OzBEQUNmLDhEQUFDYixvSkFBVUE7Ozs7OzRDQUFHOzs7Ozs7O2tEQUdoQiw4REFBQ2EsK0VBQWdCQTs7MERBQ2YsOERBQUNQLGtKQUFRQTs7Ozs7NENBQUc7Ozs7Ozs7a0RBR2QsOERBQUNPLCtFQUFnQkE7OzBEQUNmLDhEQUFDWiw4SUFBSUE7Ozs7OzRDQUFHOzs7Ozs7Ozs7Ozs7OzBDQUlaLDhEQUFDYyxvRkFBcUJBOzs7OzswQ0FDdEIsOERBQUNGLCtFQUFnQkE7O2tEQUNmLDhEQUFDVCxnSkFBTUE7Ozs7O29DQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVF4QjtHQTNFZ0JpQjs7UUFDT0QsbUVBQVVBOzs7S0FEakJDIiwic291cmNlcyI6WyIvVXNlcnMvcmlqYXN0L19fZGF0YS9fX2Rldi9FcXVhbGl0YWwvZGV2L2RjYy9kY2MtYXBwL2RjYy1hcHAvY29yZS9jb21wb25lbnRzL3VpL25hdi11c2VyLmpzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IHtcbiAgQmFkZ2VDaGVjayxcbiAgQmVsbCxcbiAgQ2hldnJvbnNVcERvd24sXG4gIEVsbGlwc2lzVmVydGljYWwsXG4gIExvZ091dCxcbiAgU3BhcmtsZXMsXG4gIFNldHRpbmdzLFxufSBmcm9tIFwibHVjaWRlLXJlYWN0XCI7XG5cbmltcG9ydCB7XG4gIEF2YXRhcixcbiAgQXZhdGFyRmFsbGJhY2ssXG4gIEF2YXRhckltYWdlLFxufSBmcm9tIFwiQGNvcmUvY29tcG9uZW50cy91aS9hdmF0YXJcIjtcbmltcG9ydCB7XG4gIERyb3Bkb3duTWVudSxcbiAgRHJvcGRvd25NZW51Q29udGVudCxcbiAgRHJvcGRvd25NZW51R3JvdXAsXG4gIERyb3Bkb3duTWVudUl0ZW0sXG4gIERyb3Bkb3duTWVudUxhYmVsLFxuICBEcm9wZG93bk1lbnVTZXBhcmF0b3IsXG4gIERyb3Bkb3duTWVudVRyaWdnZXIsXG59IGZyb20gXCJAY29yZS9jb21wb25lbnRzL3VpL2Ryb3Bkb3duLW1lbnVcIjtcbmltcG9ydCB7XG4gIFNpZGViYXJNZW51LFxuICBTaWRlYmFyTWVudUJ1dHRvbixcbiAgU2lkZWJhck1lbnVJdGVtLFxuICB1c2VTaWRlYmFyLFxufSBmcm9tIFwiQGNvcmUvY29tcG9uZW50cy91aS9zaWRlYmFyXCI7XG5cbmV4cG9ydCBmdW5jdGlvbiBOYXZVc2VyKHsgdXNlciB9KSB7XG4gIGNvbnN0IHsgaXNNb2JpbGUgfSA9IHVzZVNpZGViYXIoKTtcblxuICByZXR1cm4gKFxuICAgIDxTaWRlYmFyTWVudT5cbiAgICAgIDxTaWRlYmFyTWVudUl0ZW0+XG4gICAgICAgIDxEcm9wZG93bk1lbnU+XG4gICAgICAgICAgPERyb3Bkb3duTWVudVRyaWdnZXIgYXNDaGlsZD5cbiAgICAgICAgICAgIDxTaWRlYmFyTWVudUJ1dHRvblxuICAgICAgICAgICAgICBzaXplPVwibGdcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJkYXRhLVtzdGF0ZT1vcGVuXTpiZy1zaWRlYmFyLWFjY2VudCBkYXRhLVtzdGF0ZT1vcGVuXTp0ZXh0LXNpZGViYXItYWNjZW50LWZvcmVncm91bmRcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8QXZhdGFyIGNsYXNzTmFtZT1cImgtOCB3LTggcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgIDxBdmF0YXJJbWFnZSBzcmM9e3VzZXIuYXZhdGFyfSBhbHQ9e3VzZXIuZmlyc3RfbmFtZX0gLz5cbiAgICAgICAgICAgICAgICA8QXZhdGFyRmFsbGJhY2sgY2xhc3NOYW1lPVwicm91bmRlZC1sLWZ1bGwgYmctcHJpbWFyeSB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICBDTlxuICAgICAgICAgICAgICAgIDwvQXZhdGFyRmFsbGJhY2s+XG4gICAgICAgICAgICAgIDwvQXZhdGFyPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZmxleC0xIHRleHQtbGVmdCB0ZXh0LXNtIGxlYWRpbmctdGlnaHRcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0cnVuY2F0ZSBmb250LXNlbWlib2xkXCI+e3VzZXIubmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidHJ1bmNhdGUgdGV4dC14c1wiPnt1c2VyLmVtYWlsfTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxFbGxpcHNpc1ZlcnRpY2FsIGNsYXNzTmFtZT1cIm1sLWF1dG8gc2l6ZS00XCIgLz5cbiAgICAgICAgICAgIDwvU2lkZWJhck1lbnVCdXR0b24+XG4gICAgICAgICAgPC9Ecm9wZG93bk1lbnVUcmlnZ2VyPlxuICAgICAgICAgIDxEcm9wZG93bk1lbnVDb250ZW50XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ3LVstLXJhZGl4LWRyb3Bkb3duLW1lbnUtdHJpZ2dlci13aWR0aF0gbWluLXctNTYgcm91bmRlZC1sZ1wiXG4gICAgICAgICAgICBzaWRlPXtpc01vYmlsZSA/IFwiYm90dG9tXCIgOiBcInJpZ2h0XCJ9XG4gICAgICAgICAgICBhbGlnbj1cImVuZFwiXG4gICAgICAgICAgICBzaWRlT2Zmc2V0PXs0fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxEcm9wZG93bk1lbnVMYWJlbCBjbGFzc05hbWU9XCJwLTAgZm9udC1ub3JtYWxcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBweC0xIHB5LTEuNSB0ZXh0LWxlZnQgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgIDxBdmF0YXIgY2xhc3NOYW1lPVwiaC04IHctOCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICA8QXZhdGFySW1hZ2Ugc3JjPXt1c2VyLmF2YXRhcn0gYWx0PXt1c2VyLm5hbWV9IC8+XG4gICAgICAgICAgICAgICAgICA8QXZhdGFyRmFsbGJhY2sgY2xhc3NOYW1lPVwicm91bmRlZC1sZ1wiPkNOPC9BdmF0YXJGYWxsYmFjaz5cbiAgICAgICAgICAgICAgICA8L0F2YXRhcj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZmxleC0xIHRleHQtbGVmdCB0ZXh0LXNtIGxlYWRpbmctdGlnaHRcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRydW5jYXRlIGZvbnQtc2VtaWJvbGRcIj57dXNlci5uYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRydW5jYXRlIHRleHQteHNcIj57dXNlci5lbWFpbH08L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVMYWJlbD5cbiAgICAgICAgICAgIHsvKiA8RHJvcGRvd25NZW51U2VwYXJhdG9yIC8+ICovfVxuICAgICAgICAgICAgey8qIDxEcm9wZG93bk1lbnVHcm91cD5cbiAgICAgICAgICAgICAgPERyb3Bkb3duTWVudUl0ZW0+XG4gICAgICAgICAgICAgICAgPFNwYXJrbGVzIC8+XG4gICAgICAgICAgICAgICAgU2VwZXJhdGUgXG4gICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51SXRlbT5cbiAgICAgICAgICAgIDwvRHJvcGRvd25NZW51R3JvdXA+ICovfVxuICAgICAgICAgICAgPERyb3Bkb3duTWVudVNlcGFyYXRvciAvPlxuICAgICAgICAgICAgPERyb3Bkb3duTWVudUdyb3VwPlxuICAgICAgICAgICAgICA8RHJvcGRvd25NZW51SXRlbT5cbiAgICAgICAgICAgICAgICA8QmFkZ2VDaGVjayAvPlxuICAgICAgICAgICAgICAgIEFjY291bnRcbiAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVJdGVtPlxuICAgICAgICAgICAgICA8RHJvcGRvd25NZW51SXRlbT5cbiAgICAgICAgICAgICAgICA8U2V0dGluZ3MgLz5cbiAgICAgICAgICAgICAgICBTZXR0aW5nc1xuICAgICAgICAgICAgICA8L0Ryb3Bkb3duTWVudUl0ZW0+XG4gICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVJdGVtPlxuICAgICAgICAgICAgICAgIDxCZWxsIC8+XG4gICAgICAgICAgICAgICAgTm90aWZpY2F0aW9uc1xuICAgICAgICAgICAgICA8L0Ryb3Bkb3duTWVudUl0ZW0+XG4gICAgICAgICAgICA8L0Ryb3Bkb3duTWVudUdyb3VwPlxuICAgICAgICAgICAgPERyb3Bkb3duTWVudVNlcGFyYXRvciAvPlxuICAgICAgICAgICAgPERyb3Bkb3duTWVudUl0ZW0+XG4gICAgICAgICAgICAgIDxMb2dPdXQgLz5cbiAgICAgICAgICAgICAgTG9nIG91dFxuICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVJdGVtPlxuICAgICAgICAgIDwvRHJvcGRvd25NZW51Q29udGVudD5cbiAgICAgICAgPC9Ecm9wZG93bk1lbnU+XG4gICAgICA8L1NpZGViYXJNZW51SXRlbT5cbiAgICA8L1NpZGViYXJNZW51PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkJhZGdlQ2hlY2siLCJCZWxsIiwiQ2hldnJvbnNVcERvd24iLCJFbGxpcHNpc1ZlcnRpY2FsIiwiTG9nT3V0IiwiU3BhcmtsZXMiLCJTZXR0aW5ncyIsIkF2YXRhciIsIkF2YXRhckZhbGxiYWNrIiwiQXZhdGFySW1hZ2UiLCJEcm9wZG93bk1lbnUiLCJEcm9wZG93bk1lbnVDb250ZW50IiwiRHJvcGRvd25NZW51R3JvdXAiLCJEcm9wZG93bk1lbnVJdGVtIiwiRHJvcGRvd25NZW51TGFiZWwiLCJEcm9wZG93bk1lbnVTZXBhcmF0b3IiLCJEcm9wZG93bk1lbnVUcmlnZ2VyIiwiU2lkZWJhck1lbnUiLCJTaWRlYmFyTWVudUJ1dHRvbiIsIlNpZGViYXJNZW51SXRlbSIsInVzZVNpZGViYXIiLCJOYXZVc2VyIiwidXNlciIsImlzTW9iaWxlIiwiYXNDaGlsZCIsInNpemUiLCJjbGFzc05hbWUiLCJzcmMiLCJhdmF0YXIiLCJhbHQiLCJmaXJzdF9uYW1lIiwiZGl2Iiwic3BhbiIsIm5hbWUiLCJlbWFpbCIsInNpZGUiLCJhbGlnbiIsInNpZGVPZmZzZXQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./core/components/ui/nav-user.jsx\n"));

/***/ })

});