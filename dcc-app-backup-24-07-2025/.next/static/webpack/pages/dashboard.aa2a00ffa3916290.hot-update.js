"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "(pages-dir-browser)/./core/components/ui/nav-user.jsx":
/*!*****************************************!*\
  !*** ./core/components/ui/nav-user.jsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavUser: () => (/* binding */ NavUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_EllipsisVertical_LogOut_Settings_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BadgeCheck,Bell,ChevronsUpDown,EllipsisVertical,LogOut,Settings,Sparkles!=!lucide-react */ \"(pages-dir-browser)/__barrel_optimize__?names=BadgeCheck,Bell,ChevronsUpDown,EllipsisVertical,LogOut,Settings,Sparkles!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _core_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @core/components/ui/avatar */ \"(pages-dir-browser)/./core/components/ui/avatar.jsx\");\n/* harmony import */ var _core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @core/components/ui/dropdown-menu */ \"(pages-dir-browser)/./core/components/ui/dropdown-menu.jsx\");\n/* harmony import */ var _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @core/components/ui/sidebar */ \"(pages-dir-browser)/./core/components/ui/sidebar.jsx\");\n/* __next_internal_client_entry_do_not_use__ NavUser auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction NavUser(param) {\n    let { user } = param;\n    _s();\n    const { isMobile } = (0,_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.useSidebar)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenu, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuItem, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenu, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuTrigger, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuButton, {\n                            size: \"lg\",\n                            className: \"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.Avatar, {\n                                    className: \"h-8 w-8 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.AvatarImage, {\n                                            src: user.avatar,\n                                            alt: user.first_name + {} + user.last_name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.AvatarFallback, {\n                                            className: \"rounded-l-full bg-primary text-white\",\n                                            children: \"CN\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid flex-1 text-left text-sm leading-tight\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate font-semibold\",\n                                            children: user.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate text-xs\",\n                                            children: user.email\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_EllipsisVertical_LogOut_Settings_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__.EllipsisVertical, {\n                                    className: \"ml-auto size-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                            lineNumber: 42,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuContent, {\n                        className: \"w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg\",\n                        side: isMobile ? \"bottom\" : \"right\",\n                        align: \"end\",\n                        sideOffset: 4,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuLabel, {\n                                className: \"p-0 font-normal\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 px-1 py-1.5 text-left text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.Avatar, {\n                                            className: \"h-8 w-8 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.AvatarImage, {\n                                                    src: user.avatar,\n                                                    alt: user.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.AvatarFallback, {\n                                                    className: \"rounded-lg\",\n                                                    children: \"CN\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid flex-1 text-left text-sm leading-tight\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"truncate font-semibold\",\n                                                    children: user.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"truncate text-xs\",\n                                                    children: user.email\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuSeparator, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuGroup, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_EllipsisVertical_LogOut_Settings_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__.BadgeCheck, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Account\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_EllipsisVertical_LogOut_Settings_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Settings, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Settings\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_EllipsisVertical_LogOut_Settings_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Bell, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Notifications\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuSeparator, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_EllipsisVertical_LogOut_Settings_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__.LogOut, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Log out\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_s(NavUser, \"7bt3Tpt+2g9LjYXqO6MQJeduxl4=\", false, function() {\n    return [\n        _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.useSidebar\n    ];\n});\n_c = NavUser;\nvar _c;\n$RefreshReg$(_c, \"NavUser\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./core/components/ui/nav-user.jsx\n"));

/***/ })

});