"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "(pages-dir-browser)/./core/components/ui/nav-user.jsx":
/*!*****************************************!*\
  !*** ./core/components/ui/nav-user.jsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavUser: () => (/* binding */ NavUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_EllipsisVertical_LogOut_Settings_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BadgeCheck,Bell,ChevronsUpDown,EllipsisVertical,LogOut,Settings,Sparkles!=!lucide-react */ \"(pages-dir-browser)/__barrel_optimize__?names=BadgeCheck,Bell,ChevronsUpDown,EllipsisVertical,LogOut,Settings,Sparkles!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _core_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @core/components/ui/avatar */ \"(pages-dir-browser)/./core/components/ui/avatar.jsx\");\n/* harmony import */ var _core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @core/components/ui/dropdown-menu */ \"(pages-dir-browser)/./core/components/ui/dropdown-menu.jsx\");\n/* harmony import */ var _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @core/components/ui/sidebar */ \"(pages-dir-browser)/./core/components/ui/sidebar.jsx\");\n/* __next_internal_client_entry_do_not_use__ NavUser auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction NavUser(param) {\n    let { user } = param;\n    _s();\n    const { isMobile } = (0,_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.useSidebar)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenu, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuItem, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenu, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuTrigger, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuButton, {\n                            size: \"lg\",\n                            className: \"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.Avatar, {\n                                    className: \"h-8 w-8 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.AvatarImage, {\n                                            src: user.avatar,\n                                            alt: user.first_name + \" \" + user.last_name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.AvatarFallback, {\n                                            className: \"rounded-l-full bg-primary text-white\",\n                                            children: [\n                                                user.first_name.charAt(0),\n                                                user.last_name.charAt(0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid flex-1 text-left text-sm leading-tight\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate font-semibold\",\n                                            children: user.first_name + \" \" + user.last_name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate text-xs\",\n                                            children: user.email\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_EllipsisVertical_LogOut_Settings_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__.EllipsisVertical, {\n                                    className: \"ml-auto size-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                            lineNumber: 42,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuContent, {\n                        className: \"w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg\",\n                        side: isMobile ? \"bottom\" : \"right\",\n                        align: \"end\",\n                        sideOffset: 4,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuLabel, {\n                                className: \"p-0 font-normal\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 px-1 py-1.5 text-left text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.Avatar, {\n                                            className: \"h-8 w-8 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.AvatarImage, {\n                                                    src: user.avatar,\n                                                    alt: user.first_name + \" \" + user.last_name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.AvatarFallback, {\n                                                    className: \"rounded-l-full bg-primary text-white\",\n                                                    children: [\n                                                        user.first_name.charAt(0),\n                                                        user.last_name.charAt(0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid flex-1 text-left text-sm leading-tight\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"truncate font-semibold\",\n                                                children: user.first_name + \" \" + user.last_name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuSeparator, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuGroup, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_EllipsisVertical_LogOut_Settings_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__.BadgeCheck, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Account\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_EllipsisVertical_LogOut_Settings_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Settings, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Settings\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuSeparator, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_EllipsisVertical_LogOut_Settings_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__.LogOut, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"button block\",\n                                        onClick: ()=>supabase.auth.signOut(),\n                                        children: \"Sign Out\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/ui/nav-user.jsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_s(NavUser, \"7bt3Tpt+2g9LjYXqO6MQJeduxl4=\", false, function() {\n    return [\n        _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.useSidebar\n    ];\n});\n_c = NavUser;\nvar _c;\n$RefreshReg$(_c, \"NavUser\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL2NvcmUvY29tcG9uZW50cy91aS9uYXYtdXNlci5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBVXNCO0FBTWM7QUFTTztBQU1OO0FBRTlCLFNBQVNxQixRQUFRLEtBQVE7UUFBUixFQUFFQyxJQUFJLEVBQUUsR0FBUjs7SUFDdEIsTUFBTSxFQUFFQyxRQUFRLEVBQUUsR0FBR0gsdUVBQVVBO0lBRS9CLHFCQUNFLDhEQUFDSCxvRUFBV0E7a0JBQ1YsNEVBQUNFLHdFQUFlQTtzQkFDZCw0RUFBQ1QsMkVBQVlBOztrQ0FDWCw4REFBQ00sa0ZBQW1CQTt3QkFBQ1EsT0FBTztrQ0FDMUIsNEVBQUNOLDBFQUFpQkE7NEJBQ2hCTyxNQUFLOzRCQUNMQyxXQUFVOzs4Q0FFViw4REFBQ25CLDhEQUFNQTtvQ0FBQ21CLFdBQVU7O3NEQUNoQiw4REFBQ2pCLG1FQUFXQTs0Q0FDVmtCLEtBQUtMLEtBQUtNLE1BQU07NENBQ2hCQyxLQUFLUCxLQUFLUSxVQUFVLEdBQUcsTUFBTVIsS0FBS1MsU0FBUzs7Ozs7O3NEQUU3Qyw4REFBQ3ZCLHNFQUFjQTs0Q0FBQ2tCLFdBQVU7O2dEQUN2QkosS0FBS1EsVUFBVSxDQUFDRSxNQUFNLENBQUM7Z0RBQ3ZCVixLQUFLUyxTQUFTLENBQUNDLE1BQU0sQ0FBQzs7Ozs7Ozs7Ozs7Ozs4Q0FHM0IsOERBQUNDO29DQUFJUCxXQUFVOztzREFDYiw4REFBQ1E7NENBQUtSLFdBQVU7c0RBQ2JKLEtBQUtRLFVBQVUsR0FBRyxNQUFNUixLQUFLUyxTQUFTOzs7Ozs7c0RBRXpDLDhEQUFDRzs0Q0FBS1IsV0FBVTtzREFBb0JKLEtBQUthLEtBQUs7Ozs7Ozs7Ozs7Ozs4Q0FFaEQsOERBQUNoQywwSkFBZ0JBO29DQUFDdUIsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBR2hDLDhEQUFDZixrRkFBbUJBO3dCQUNsQmUsV0FBVTt3QkFDVlUsTUFBTWIsV0FBVyxXQUFXO3dCQUM1QmMsT0FBTTt3QkFDTkMsWUFBWTs7MENBRVosOERBQUN4QixnRkFBaUJBO2dDQUFDWSxXQUFVOzBDQUMzQiw0RUFBQ087b0NBQUlQLFdBQVU7O3NEQUNiLDhEQUFDbkIsOERBQU1BOzRDQUFDbUIsV0FBVTs7OERBQ2hCLDhEQUFDakIsbUVBQVdBO29EQUNWa0IsS0FBS0wsS0FBS00sTUFBTTtvREFDaEJDLEtBQUtQLEtBQUtRLFVBQVUsR0FBRyxNQUFNUixLQUFLUyxTQUFTOzs7Ozs7OERBRTdDLDhEQUFDdkIsc0VBQWNBO29EQUFDa0IsV0FBVTs7d0RBQ3ZCSixLQUFLUSxVQUFVLENBQUNFLE1BQU0sQ0FBQzt3REFDdkJWLEtBQUtTLFNBQVMsQ0FBQ0MsTUFBTSxDQUFDOzs7Ozs7Ozs7Ozs7O3NEQUczQiw4REFBQ0M7NENBQUlQLFdBQVU7c0RBQ2IsNEVBQUNRO2dEQUFLUixXQUFVOzBEQUNiSixLQUFLUSxVQUFVLEdBQUcsTUFBTVIsS0FBS1MsU0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FZL0MsOERBQUNoQixvRkFBcUJBOzs7OzswQ0FDdEIsOERBQUNILGdGQUFpQkE7O2tEQUNoQiw4REFBQ0MsK0VBQWdCQTs7MERBQ2YsOERBQUNiLG9KQUFVQTs7Ozs7NENBQUc7Ozs7Ozs7a0RBR2hCLDhEQUFDYSwrRUFBZ0JBOzswREFDZiw4REFBQ1Asa0pBQVFBOzs7Ozs0Q0FBRzs7Ozs7Ozs7Ozs7OzswQ0FJaEIsOERBQUNTLG9GQUFxQkE7Ozs7OzBDQUN0Qiw4REFBQ0YsK0VBQWdCQTs7a0RBQ2YsOERBQUNULGdKQUFNQTs7Ozs7a0RBRVAsOERBQUNtQzt3Q0FDQ2IsV0FBVTt3Q0FDVmMsU0FBUyxJQUFNQyxTQUFTQyxJQUFJLENBQUNDLE9BQU87a0RBQ3JDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBU2Y7R0ExRmdCdEI7O1FBQ09ELG1FQUFVQTs7O0tBRGpCQyIsInNvdXJjZXMiOlsiL1VzZXJzL3JpamFzdC9fX2RhdGEvX19kZXYvRXF1YWxpdGFsL2Rldi9kY2MvZGNjLWFwcC9kY2MtYXBwL2NvcmUvY29tcG9uZW50cy91aS9uYXYtdXNlci5qc3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7XG4gIEJhZGdlQ2hlY2ssXG4gIEJlbGwsXG4gIENoZXZyb25zVXBEb3duLFxuICBFbGxpcHNpc1ZlcnRpY2FsLFxuICBMb2dPdXQsXG4gIFNwYXJrbGVzLFxuICBTZXR0aW5ncyxcbn0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xuXG5pbXBvcnQge1xuICBBdmF0YXIsXG4gIEF2YXRhckZhbGxiYWNrLFxuICBBdmF0YXJJbWFnZSxcbn0gZnJvbSBcIkBjb3JlL2NvbXBvbmVudHMvdWkvYXZhdGFyXCI7XG5pbXBvcnQge1xuICBEcm9wZG93bk1lbnUsXG4gIERyb3Bkb3duTWVudUNvbnRlbnQsXG4gIERyb3Bkb3duTWVudUdyb3VwLFxuICBEcm9wZG93bk1lbnVJdGVtLFxuICBEcm9wZG93bk1lbnVMYWJlbCxcbiAgRHJvcGRvd25NZW51U2VwYXJhdG9yLFxuICBEcm9wZG93bk1lbnVUcmlnZ2VyLFxufSBmcm9tIFwiQGNvcmUvY29tcG9uZW50cy91aS9kcm9wZG93bi1tZW51XCI7XG5pbXBvcnQge1xuICBTaWRlYmFyTWVudSxcbiAgU2lkZWJhck1lbnVCdXR0b24sXG4gIFNpZGViYXJNZW51SXRlbSxcbiAgdXNlU2lkZWJhcixcbn0gZnJvbSBcIkBjb3JlL2NvbXBvbmVudHMvdWkvc2lkZWJhclwiO1xuXG5leHBvcnQgZnVuY3Rpb24gTmF2VXNlcih7IHVzZXIgfSkge1xuICBjb25zdCB7IGlzTW9iaWxlIH0gPSB1c2VTaWRlYmFyKCk7XG5cbiAgcmV0dXJuIChcbiAgICA8U2lkZWJhck1lbnU+XG4gICAgICA8U2lkZWJhck1lbnVJdGVtPlxuICAgICAgICA8RHJvcGRvd25NZW51PlxuICAgICAgICAgIDxEcm9wZG93bk1lbnVUcmlnZ2VyIGFzQ2hpbGQ+XG4gICAgICAgICAgICA8U2lkZWJhck1lbnVCdXR0b25cbiAgICAgICAgICAgICAgc2l6ZT1cImxnXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZGF0YS1bc3RhdGU9b3Blbl06Ymctc2lkZWJhci1hY2NlbnQgZGF0YS1bc3RhdGU9b3Blbl06dGV4dC1zaWRlYmFyLWFjY2VudC1mb3JlZ3JvdW5kXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPEF2YXRhciBjbGFzc05hbWU9XCJoLTggdy04IHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICA8QXZhdGFySW1hZ2VcbiAgICAgICAgICAgICAgICAgIHNyYz17dXNlci5hdmF0YXJ9XG4gICAgICAgICAgICAgICAgICBhbHQ9e3VzZXIuZmlyc3RfbmFtZSArIFwiIFwiICsgdXNlci5sYXN0X25hbWV9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8QXZhdGFyRmFsbGJhY2sgY2xhc3NOYW1lPVwicm91bmRlZC1sLWZ1bGwgYmctcHJpbWFyeSB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICB7dXNlci5maXJzdF9uYW1lLmNoYXJBdCgwKX1cbiAgICAgICAgICAgICAgICAgIHt1c2VyLmxhc3RfbmFtZS5jaGFyQXQoMCl9XG4gICAgICAgICAgICAgICAgPC9BdmF0YXJGYWxsYmFjaz5cbiAgICAgICAgICAgICAgPC9BdmF0YXI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBmbGV4LTEgdGV4dC1sZWZ0IHRleHQtc20gbGVhZGluZy10aWdodFwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRydW5jYXRlIGZvbnQtc2VtaWJvbGRcIj5cbiAgICAgICAgICAgICAgICAgIHt1c2VyLmZpcnN0X25hbWUgKyBcIiBcIiArIHVzZXIubGFzdF9uYW1lfVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0cnVuY2F0ZSB0ZXh0LXhzXCI+e3VzZXIuZW1haWx9PC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPEVsbGlwc2lzVmVydGljYWwgY2xhc3NOYW1lPVwibWwtYXV0byBzaXplLTRcIiAvPlxuICAgICAgICAgICAgPC9TaWRlYmFyTWVudUJ1dHRvbj5cbiAgICAgICAgICA8L0Ryb3Bkb3duTWVudVRyaWdnZXI+XG4gICAgICAgICAgPERyb3Bkb3duTWVudUNvbnRlbnRcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInctWy0tcmFkaXgtZHJvcGRvd24tbWVudS10cmlnZ2VyLXdpZHRoXSBtaW4tdy01NiByb3VuZGVkLWxnXCJcbiAgICAgICAgICAgIHNpZGU9e2lzTW9iaWxlID8gXCJib3R0b21cIiA6IFwicmlnaHRcIn1cbiAgICAgICAgICAgIGFsaWduPVwiZW5kXCJcbiAgICAgICAgICAgIHNpZGVPZmZzZXQ9ezR9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPERyb3Bkb3duTWVudUxhYmVsIGNsYXNzTmFtZT1cInAtMCBmb250LW5vcm1hbFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHB4LTEgcHktMS41IHRleHQtbGVmdCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgPEF2YXRhciBjbGFzc05hbWU9XCJoLTggdy04IHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxBdmF0YXJJbWFnZVxuICAgICAgICAgICAgICAgICAgICBzcmM9e3VzZXIuYXZhdGFyfVxuICAgICAgICAgICAgICAgICAgICBhbHQ9e3VzZXIuZmlyc3RfbmFtZSArIFwiIFwiICsgdXNlci5sYXN0X25hbWV9XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPEF2YXRhckZhbGxiYWNrIGNsYXNzTmFtZT1cInJvdW5kZWQtbC1mdWxsIGJnLXByaW1hcnkgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICB7dXNlci5maXJzdF9uYW1lLmNoYXJBdCgwKX1cbiAgICAgICAgICAgICAgICAgICAge3VzZXIubGFzdF9uYW1lLmNoYXJBdCgwKX1cbiAgICAgICAgICAgICAgICAgIDwvQXZhdGFyRmFsbGJhY2s+XG4gICAgICAgICAgICAgICAgPC9BdmF0YXI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGZsZXgtMSB0ZXh0LWxlZnQgdGV4dC1zbSBsZWFkaW5nLXRpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0cnVuY2F0ZSBmb250LXNlbWlib2xkXCI+XG4gICAgICAgICAgICAgICAgICAgIHt1c2VyLmZpcnN0X25hbWUgKyBcIiBcIiArIHVzZXIubGFzdF9uYW1lfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvRHJvcGRvd25NZW51TGFiZWw+XG4gICAgICAgICAgICB7LyogPERyb3Bkb3duTWVudVNlcGFyYXRvciAvPiAqL31cbiAgICAgICAgICAgIHsvKiA8RHJvcGRvd25NZW51R3JvdXA+XG4gICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVJdGVtPlxuICAgICAgICAgICAgICAgIDxTcGFya2xlcyAvPlxuICAgICAgICAgICAgICAgIFNlcGVyYXRlIFxuICAgICAgICAgICAgICA8L0Ryb3Bkb3duTWVudUl0ZW0+XG4gICAgICAgICAgICA8L0Ryb3Bkb3duTWVudUdyb3VwPiAqL31cbiAgICAgICAgICAgIDxEcm9wZG93bk1lbnVTZXBhcmF0b3IgLz5cbiAgICAgICAgICAgIDxEcm9wZG93bk1lbnVHcm91cD5cbiAgICAgICAgICAgICAgPERyb3Bkb3duTWVudUl0ZW0+XG4gICAgICAgICAgICAgICAgPEJhZGdlQ2hlY2sgLz5cbiAgICAgICAgICAgICAgICBBY2NvdW50XG4gICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51SXRlbT5cbiAgICAgICAgICAgICAgPERyb3Bkb3duTWVudUl0ZW0+XG4gICAgICAgICAgICAgICAgPFNldHRpbmdzIC8+XG4gICAgICAgICAgICAgICAgU2V0dGluZ3NcbiAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVJdGVtPlxuICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVHcm91cD5cbiAgICAgICAgICAgIDxEcm9wZG93bk1lbnVTZXBhcmF0b3IgLz5cbiAgICAgICAgICAgIDxEcm9wZG93bk1lbnVJdGVtPlxuICAgICAgICAgICAgICA8TG9nT3V0IC8+XG4gICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJidXR0b24gYmxvY2tcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHN1cGFiYXNlLmF1dGguc2lnbk91dCgpfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgU2lnbiBPdXRcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L0Ryb3Bkb3duTWVudUl0ZW0+XG4gICAgICAgICAgPC9Ecm9wZG93bk1lbnVDb250ZW50PlxuICAgICAgICA8L0Ryb3Bkb3duTWVudT5cbiAgICAgIDwvU2lkZWJhck1lbnVJdGVtPlxuICAgIDwvU2lkZWJhck1lbnU+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiQmFkZ2VDaGVjayIsIkJlbGwiLCJDaGV2cm9uc1VwRG93biIsIkVsbGlwc2lzVmVydGljYWwiLCJMb2dPdXQiLCJTcGFya2xlcyIsIlNldHRpbmdzIiwiQXZhdGFyIiwiQXZhdGFyRmFsbGJhY2siLCJBdmF0YXJJbWFnZSIsIkRyb3Bkb3duTWVudSIsIkRyb3Bkb3duTWVudUNvbnRlbnQiLCJEcm9wZG93bk1lbnVHcm91cCIsIkRyb3Bkb3duTWVudUl0ZW0iLCJEcm9wZG93bk1lbnVMYWJlbCIsIkRyb3Bkb3duTWVudVNlcGFyYXRvciIsIkRyb3Bkb3duTWVudVRyaWdnZXIiLCJTaWRlYmFyTWVudSIsIlNpZGViYXJNZW51QnV0dG9uIiwiU2lkZWJhck1lbnVJdGVtIiwidXNlU2lkZWJhciIsIk5hdlVzZXIiLCJ1c2VyIiwiaXNNb2JpbGUiLCJhc0NoaWxkIiwic2l6ZSIsImNsYXNzTmFtZSIsInNyYyIsImF2YXRhciIsImFsdCIsImZpcnN0X25hbWUiLCJsYXN0X25hbWUiLCJjaGFyQXQiLCJkaXYiLCJzcGFuIiwiZW1haWwiLCJzaWRlIiwiYWxpZ24iLCJzaWRlT2Zmc2V0IiwiYnV0dG9uIiwib25DbGljayIsInN1cGFiYXNlIiwiYXV0aCIsInNpZ25PdXQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./core/components/ui/nav-user.jsx\n"));

/***/ })

});